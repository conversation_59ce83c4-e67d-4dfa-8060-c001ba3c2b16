-- 电子价签系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS electronic_price_tag 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE electronic_price_tag;

-- 设置时区
SET time_zone = '+08:00';

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    real_name VARCHAR(50),
    avatar VARCHAR(255),
    status TINYINT DEFAULT 1 COMMENT '1-正常,0-禁用',
    merchant_id VARCHAR(32) NOT NULL,
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建角色表
CREATE TABLE IF NOT EXISTS roles (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(255),
    merchant_id VARCHAR(32) NOT NULL,
    status TINYINT DEFAULT 1 COMMENT '1-正常,0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建权限表
CREATE TABLE IF NOT EXISTS permissions (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(100) NOT NULL UNIQUE,
    type VARCHAR(20) NOT NULL COMMENT 'menu,button,api',
    path VARCHAR(255),
    method VARCHAR(10),
    parent_id INT UNSIGNED DEFAULT 0,
    sort INT DEFAULT 0,
    icon VARCHAR(50),
    component VARCHAR(255),
    status TINYINT DEFAULT 1 COMMENT '1-正常,0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_parent_id (parent_id),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建商户表
CREATE TABLE IF NOT EXISTS merchants (
    id VARCHAR(32) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    contact VARCHAR(50),
    phone VARCHAR(20),
    email VARCHAR(100),
    address VARCHAR(255),
    logo VARCHAR(255),
    status TINYINT DEFAULT 1 COMMENT '1-正常,0-禁用',
    expire_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建用户角色关联表
CREATE TABLE IF NOT EXISTS user_roles (
    user_id INT UNSIGNED NOT NULL,
    role_id INT UNSIGNED NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
    role_id INT UNSIGNED NOT NULL,
    permission_id INT UNSIGNED NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建设备表
CREATE TABLE IF NOT EXISTS devices (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    sn VARCHAR(64) NOT NULL UNIQUE COMMENT '设备序列号',
    name VARCHAR(100) NOT NULL,
    model VARCHAR(50) NOT NULL COMMENT '设备型号',
    version VARCHAR(20) COMMENT '固件版本',
    merchant_id VARCHAR(32) NOT NULL,
    store_id INT UNSIGNED,
    location_id INT UNSIGNED,
    template_id INT UNSIGNED,
    product_id INT UNSIGNED,
    status TINYINT DEFAULT 0 COMMENT '0-离线,1-在线,2-故障',
    battery TINYINT DEFAULT 0 COMMENT '电池电量百分比',
    rssi INT DEFAULT 0 COMMENT '信号强度',
    last_seen TIMESTAMP NULL COMMENT '最后在线时间',
    secret_key VARCHAR(64) COMMENT '设备密钥',
    is_activated BOOLEAN DEFAULT FALSE COMMENT '是否已激活',
    activated_at TIMESTAMP NULL COMMENT '激活时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_store_id (store_id),
    INDEX idx_location_id (location_id),
    INDEX idx_template_id (template_id),
    INDEX idx_product_id (product_id),
    INDEX idx_deleted_at (deleted_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认数据
INSERT IGNORE INTO merchants (id, name, code, status) VALUES 
('M000001', '默认商户', 'default', 1);

INSERT IGNORE INTO permissions (name, code, type, path, method) VALUES
('用户列表', 'system:user:list', 'api', '/api/users', 'GET'),
('创建用户', 'system:user:create', 'api', '/api/users', 'POST'),
('更新用户', 'system:user:update', 'api', '/api/users/*', 'PUT'),
('删除用户', 'system:user:delete', 'api', '/api/users/*', 'DELETE'),
('角色列表', 'system:role:list', 'api', '/api/roles', 'GET'),
('创建角色', 'system:role:create', 'api', '/api/roles', 'POST'),
('更新角色', 'system:role:update', 'api', '/api/roles/*', 'PUT'),
('删除角色', 'system:role:delete', 'api', '/api/roles/*', 'DELETE'),
('设备列表', 'device:list', 'api', '/api/devices', 'GET'),
('创建设备', 'device:create', 'api', '/api/devices', 'POST'),
('更新设备', 'device:update', 'api', '/api/devices/*', 'PUT'),
('删除设备', 'device:delete', 'api', '/api/devices/*', 'DELETE'),
('设备控制', 'device:control', 'api', '/api/devices/*/control', 'POST'),
('模板列表', 'template:list', 'api', '/api/templates', 'GET'),
('创建模板', 'template:create', 'api', '/api/templates', 'POST'),
('更新模板', 'template:update', 'api', '/api/templates/*', 'PUT'),
('删除模板', 'template:delete', 'api', '/api/templates/*', 'DELETE'),
('商品列表', 'product:list', 'api', '/api/products', 'GET'),
('创建商品', 'product:create', 'api', '/api/products', 'POST'),
('更新商品', 'product:update', 'api', '/api/products/*', 'PUT'),
('删除商品', 'product:delete', 'api', '/api/products/*', 'DELETE'),
('OTA列表', 'ota:list', 'api', '/api/ota/tasks', 'GET'),
('创建OTA任务', 'ota:create', 'api', '/api/ota/tasks', 'POST'),
('更新OTA任务', 'ota:update', 'api', '/api/ota/tasks/*', 'PUT'),
('删除OTA任务', 'ota:delete', 'api', '/api/ota/tasks/*', 'DELETE');

INSERT IGNORE INTO roles (name, code, description, merchant_id, status) VALUES
('超级管理员', 'admin', '系统超级管理员', 'M000001', 1);

-- 获取管理员角色ID
SET @admin_role_id = (SELECT id FROM roles WHERE code = 'admin' LIMIT 1);

-- 为管理员角色分配所有权限
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT @admin_role_id, id FROM permissions;

INSERT IGNORE INTO users (username, password, email, real_name, status, merchant_id) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZFzDO6OzO6T7.jbNjpjSuADAa', '<EMAIL>', '系统管理员', 1, 'M000001');

-- 获取管理员用户ID
SET @admin_user_id = (SELECT id FROM users WHERE username = 'admin' LIMIT 1);

-- 为管理员用户分配管理员角色
INSERT IGNORE INTO user_roles (user_id, role_id) VALUES (@admin_user_id, @admin_role_id);

-- 创建 Casbin 策略表
CREATE TABLE IF NOT EXISTS casbin_rule (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    ptype VARCHAR(100),
    v0 VARCHAR(100),
    v1 VARCHAR(100),
    v2 VARCHAR(100),
    v3 VARCHAR(100),
    v4 VARCHAR(100),
    v5 VARCHAR(100),
    INDEX idx_ptype (ptype),
    INDEX idx_v0 (v0),
    INDEX idx_v1 (v1)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
