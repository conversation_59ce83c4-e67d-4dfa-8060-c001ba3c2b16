# 电子价签系统 - 快速开始指南

本文档将指导您如何快速搭建和运行电子价签管理系统。

## 📋 系统要求

### 开发环境
- **Go**: 1.21 或更高版本
- **Node.js**: 16.0 或更高版本
- **MySQL**: 8.0 或更高版本
- **Redis**: 6.0 或更高版本
- **EMQX**: 5.0 或更高版本（MQTT Broker）

### 推荐配置
- **CPU**: 2核心或更多
- **内存**: 4GB 或更多
- **存储**: 50GB 或更多可用空间

## 🚀 快速启动

### 方式一：Docker 一键部署（推荐）

1. **克隆项目**
```bash
git clone https://github.com/your-repo/electronic-price-tag.git
cd electronic-price-tag
```

2. **启动所有服务**
```bash
docker-compose up -d
```

3. **等待服务启动完成**
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

4. **访问系统**
- 后端API: http://localhost:8080
- 前端管理: http://localhost:3000
- EMQX控制台: http://localhost:18083 (admin/public)

### 方式二：本地开发环境

#### 1. 准备数据库和中间件

**使用 Docker 启动基础服务：**
```bash
# 启动 MySQL、Redis、EMQX
docker-compose up -d mysql redis emqx
```

**或手动安装：**
- MySQL 8.0+
- Redis 6.0+
- EMQX 5.0+

#### 2. 初始化数据库

```bash
# 连接到 MySQL
mysql -h127.0.0.1 -P3307 -uroot -p123456

# 执行初始化脚本
source scripts/init.sql
```

#### 3. 启动后端服务

```bash
# 安装 Go 依赖
go mod download
go mod tidy

# 复制配置文件
cp configs/config.example.yaml configs/config.yaml

# 编辑配置文件（如需要）
vim configs/config.yaml

# 启动后端服务
go run cmd/server/main.go

# 或使用 Makefile
make run

# 开发模式（热重载）
make dev
```

#### 4. 启动前端服务

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

#### 5. 启动小程序（可选）

```bash
cd miniprogram

# 安装依赖
npm install

# 启动开发
npm run dev

# 构建微信小程序
npm run build:mp-weixin
```

### 方式三：一键启动脚本

```bash
# 给脚本执行权限
chmod +x scripts/start.sh

# 启动系统
./scripts/start.sh start

# 查看状态
./scripts/start.sh status

# 停止系统
./scripts/start.sh stop
```

## 🔧 配置说明

### 后端配置文件 (configs/config.yaml)

```yaml
# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  mode: "debug"  # debug, release, test

# 数据库配置
database:
  host: "localhost"
  port: 3307
  username: "root"
  password: "123456"
  database: "electronic_price_tag"
  charset: "utf8mb4"
  max_idle_conns: 10
  max_open_conns: 100

# Redis配置
redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 10

# MQTT配置
mqtt:
  broker: "tcp://localhost:1883"
  username: ""
  password: ""
  client_id: "electronic-price-tag-server"

# JWT配置
jwt:
  secret: "electronic-price-tag-secret-key"
  expire_time: 168h  # 7天

# 日志配置
log:
  level: "info"
  file_path: "./logs/app.log"
  max_size: 100
  max_backups: 5
  max_age: 30
```

### 前端配置

前端配置主要在 `frontend/src/utils/request.js` 中：

```javascript
// API 基础地址
const service = axios.create({
  baseURL: '/api',  // 开发环境通过代理
  timeout: 30000
})
```

生产环境需要修改为实际的API地址。

## 🔑 默认账号

系统初始化后会创建默认管理员账号：

- **用户名**: `admin`
- **密码**: `123456`

**⚠️ 重要提示**: 首次登录后请立即修改默认密码！

## 📱 功能模块

### 1. 用户管理
- 用户注册、登录、权限管理
- 角色分配、权限控制
- 多商户支持

### 2. 设备管理
- 设备注册、激活
- 设备状态监控
- 远程控制命令
- 设备分组管理

### 3. 模板管理
- 可视化模板设计器
- 模板元素管理
- 模板渲染引擎
- EPD数据生成

### 4. 商品管理
- 商品信息管理
- 价格管理和历史记录
- 商品分类管理
- 批量操作

### 5. OTA升级
- 固件版本管理
- 升级任务创建
- 升级进度监控
- 差分包生成

## 🔌 设备接入

### MQTT 主题规范

```
# 设备上报
device/{sn}/status      # 设备状态
device/{sn}/heartbeat   # 心跳消息
device/{sn}/response    # 命令响应
device/{sn}/log         # 设备日志
device/{sn}/ota         # OTA进度

# 服务器下发
device/{sn}/command     # 控制命令
device/{sn}/update      # 内容更新
device/{sn}/ota/update  # OTA升级
```

### 设备注册流程

1. 设备上电，连接WiFi
2. 调用注册接口：`POST /api/device/register`
3. 获取MQTT连接参数
4. 建立MQTT连接
5. 开始心跳和状态上报

### 消息格式示例

**心跳消息：**
```json
{
  "sn": "ESL001",
  "timestamp": "2023-12-01T10:30:00Z",
  "battery": 85,
  "rssi": -45
}
```

**状态上报：**
```json
{
  "sn": "ESL001",
  "status": 1,
  "battery": 85,
  "rssi": -45,
  "version": "v1.2.3",
  "last_seen": "2023-12-01T10:30:00Z"
}
```

## 🐛 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库配置信息正确
- 检查防火墙设置

### 2. Redis连接失败
- 检查Redis服务是否启动
- 确认Redis配置信息正确

### 3. MQTT连接失败
- 检查EMQX服务是否启动
- 确认MQTT配置信息正确
- 检查网络连接

### 4. 前端无法访问后端API
- 检查后端服务是否启动
- 确认端口配置正确
- 检查CORS配置

### 5. 设备无法连接
- 检查设备网络连接
- 确认MQTT配置正确
- 检查设备认证信息

## 📞 技术支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

- 📧 邮箱: <EMAIL>
- 💬 QQ群: 123456789
- 📱 微信群: 扫码加入
- 🐛 Issue: [GitHub Issues](https://github.com/your-repo/electronic-price-tag/issues)

## 📚 更多文档

- [API文档](./API.md)
- [部署指南](./DEPLOYMENT.md)
- [开发指南](./DEVELOPMENT.md)
- [设备接入指南](./DEVICE_INTEGRATION.md)
- [故障排除](./TROUBLESHOOTING.md)
