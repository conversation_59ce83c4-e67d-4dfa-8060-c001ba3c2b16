package service

import (
	"electronic-price-tag/internal/model"
	"electronic-price-tag/pkg/logger"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type TemplateService interface {
	GetTemplates(merchantID string, page, pageSize int, keyword string) (*TemplateListResponse, error)
	GetTemplateByID(id uint) (*model.Template, error)
	CreateTemplate(req *CreateTemplateRequest) (*model.Template, error)
	UpdateTemplate(id uint, req *UpdateTemplateRequest) error
	DeleteTemplate(id uint) error
	GetTemplateElements(templateID uint) ([]model.TemplateElement, error)
	CreateTemplateElement(req *CreateTemplateElementRequest) (*model.TemplateElement, error)
	UpdateTemplateElement(id uint, req *UpdateTemplateElementRequest) error
	DeleteTemplateElement(id uint) error
	RenderTemplate(templateID uint, data map[string]interface{}) ([]byte, error)
	GenerateEPD(templateID uint, data map[string]interface{}) ([]byte, error)
}

type templateService struct {
	db  *gorm.DB
	rdb *redis.Client
}

type TemplateListResponse struct {
	Total int              `json:"total"`
	List  []model.Template `json:"list"`
}

type CreateTemplateRequest struct {
	Name       string `json:"name" binding:"required"`
	Type       string `json:"type" binding:"required"`
	Width      int    `json:"width" binding:"required"`
	Height     int    `json:"height" binding:"required"`
	Background string `json:"background"`
	MerchantID string `json:"merchant_id" binding:"required"`
	IsDefault  bool   `json:"is_default"`
	Config     string `json:"config"`
}

type UpdateTemplateRequest struct {
	Name       string `json:"name"`
	Type       string `json:"type"`
	Width      *int   `json:"width"`
	Height     *int   `json:"height"`
	Background string `json:"background"`
	IsDefault  *bool  `json:"is_default"`
	Status     *int   `json:"status"`
	Config     string `json:"config"`
}

type CreateTemplateElementRequest struct {
	TemplateID   uint   `json:"template_id" binding:"required"`
	Type         string `json:"type" binding:"required"`
	Name         string `json:"name" binding:"required"`
	X            int    `json:"x" binding:"required"`
	Y            int    `json:"y" binding:"required"`
	Width        int    `json:"width" binding:"required"`
	Height       int    `json:"height" binding:"required"`
	FontSize     int    `json:"font_size"`
	FontWeight   string `json:"font_weight"`
	Color        string `json:"color"`
	Align        string `json:"align"`
	DataField    string `json:"data_field"`
	DefaultValue string `json:"default_value"`
	Format       string `json:"format"`
	Sort         int    `json:"sort"`
	Config       string `json:"config"`
}

type UpdateTemplateElementRequest struct {
	Type         string `json:"type"`
	Name         string `json:"name"`
	X            *int   `json:"x"`
	Y            *int   `json:"y"`
	Width        *int   `json:"width"`
	Height       *int   `json:"height"`
	FontSize     *int   `json:"font_size"`
	FontWeight   string `json:"font_weight"`
	Color        string `json:"color"`
	Align        string `json:"align"`
	DataField    string `json:"data_field"`
	DefaultValue string `json:"default_value"`
	Format       string `json:"format"`
	Sort         *int   `json:"sort"`
	Config       string `json:"config"`
}

func NewTemplateService(db *gorm.DB, rdb *redis.Client) TemplateService {
	return &templateService{
		db:  db,
		rdb: rdb,
	}
}

// GetTemplates 获取模板列表
func (s *templateService) GetTemplates(merchantID string, page, pageSize int, keyword string) (*TemplateListResponse, error) {
	var templates []model.Template
	var total int64

	query := s.db.Model(&model.Template{}).Preload("Elements")

	// 商户过滤
	if merchantID != "" {
		query = query.Where("merchant_id = ?", merchantID)
	}

	// 关键词搜索
	if keyword != "" {
		query = query.Where("name LIKE ? OR type LIKE ?",
			fmt.Sprintf("%%%s%%", keyword),
			fmt.Sprintf("%%%s%%", keyword))
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count templates: %v", err)
		return nil, errors.New("获取模板总数失败")
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&templates).Error; err != nil {
		logger.Error("Failed to get templates: %v", err)
		return nil, errors.New("获取模板列表失败")
	}

	return &TemplateListResponse{
		Total: int(total),
		List:  templates,
	}, nil
}

// GetTemplateByID 根据ID获取模板
func (s *templateService) GetTemplateByID(id uint) (*model.Template, error) {
	var template model.Template
	if err := s.db.Preload("Elements", func(db *gorm.DB) *gorm.DB {
		return db.Order("sort ASC")
	}).Where("id = ?", id).First(&template).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("模板不存在")
		}
		logger.Error("Failed to get template by id: %v", err)
		return nil, errors.New("获取模板信息失败")
	}

	return &template, nil
}

// CreateTemplate 创建模板
func (s *templateService) CreateTemplate(req *CreateTemplateRequest) (*model.Template, error) {
	// 检查模板名称是否已存在
	var existTemplate model.Template
	if err := s.db.Where("name = ? AND merchant_id = ?", req.Name, req.MerchantID).First(&existTemplate).Error; err == nil {
		return nil, errors.New("模板名称已存在")
	}

	// 如果设置为默认模板，先取消其他默认模板
	if req.IsDefault {
		s.db.Model(&model.Template{}).Where("merchant_id = ? AND is_default = ?", req.MerchantID, true).
			Update("is_default", false)
	}

	// 创建模板
	template := model.Template{
		Name:       req.Name,
		Type:       req.Type,
		Width:      req.Width,
		Height:     req.Height,
		Background: req.Background,
		MerchantID: req.MerchantID,
		IsDefault:  req.IsDefault,
		Status:     1,
		Config:     req.Config,
	}

	if template.Background == "" {
		template.Background = "#FFFFFF"
	}

	if err := s.db.Create(&template).Error; err != nil {
		logger.Error("Failed to create template: %v", err)
		return nil, errors.New("创建模板失败")
	}

	return &template, nil
}

// UpdateTemplate 更新模板
func (s *templateService) UpdateTemplate(id uint, req *UpdateTemplateRequest) error {
	// 检查模板是否存在
	var template model.Template
	if err := s.db.Where("id = ?", id).First(&template).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("模板不存在")
		}
		return errors.New("查询模板失败")
	}

	// 检查模板名称是否已被其他模板使用
	if req.Name != "" && req.Name != template.Name {
		var existTemplate model.Template
		if err := s.db.Where("name = ? AND merchant_id = ? AND id != ?", req.Name, template.MerchantID, id).
			First(&existTemplate).Error; err == nil {
			return errors.New("模板名称已被其他模板使用")
		}
	}

	// 如果设置为默认模板，先取消其他默认模板
	if req.IsDefault != nil && *req.IsDefault {
		s.db.Model(&model.Template{}).Where("merchant_id = ? AND is_default = ? AND id != ?", 
			template.MerchantID, true, id).Update("is_default", false)
	}

	// 更新模板信息
	updates := map[string]interface{}{}
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Type != "" {
		updates["type"] = req.Type
	}
	if req.Width != nil {
		updates["width"] = *req.Width
	}
	if req.Height != nil {
		updates["height"] = *req.Height
	}
	if req.Background != "" {
		updates["background"] = req.Background
	}
	if req.IsDefault != nil {
		updates["is_default"] = *req.IsDefault
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	if req.Config != "" {
		updates["config"] = req.Config
	}

	if len(updates) > 0 {
		if err := s.db.Model(&template).Updates(updates).Error; err != nil {
			logger.Error("Failed to update template: %v", err)
			return errors.New("更新模板失败")
		}
	}

	return nil
}

// DeleteTemplate 删除模板
func (s *templateService) DeleteTemplate(id uint) error {
	// 检查模板是否存在
	var template model.Template
	if err := s.db.Where("id = ?", id).First(&template).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("模板不存在")
		}
		return errors.New("查询模板失败")
	}

	// 检查是否有设备在使用此模板
	var deviceCount int64
	if err := s.db.Model(&model.Device{}).Where("template_id = ?", id).Count(&deviceCount).Error; err != nil {
		return errors.New("检查模板使用情况失败")
	}

	if deviceCount > 0 {
		return errors.New("模板正在被设备使用，无法删除")
	}

	// 删除模板元素
	if err := s.db.Where("template_id = ?", id).Delete(&model.TemplateElement{}).Error; err != nil {
		logger.Error("Failed to delete template elements: %v", err)
	}

	// 软删除模板
	if err := s.db.Delete(&template).Error; err != nil {
		logger.Error("Failed to delete template: %v", err)
		return errors.New("删除模板失败")
	}

	return nil
}

// GetTemplateElements 获取模板元素
func (s *templateService) GetTemplateElements(templateID uint) ([]model.TemplateElement, error) {
	var elements []model.TemplateElement
	if err := s.db.Where("template_id = ?", templateID).Order("sort ASC").Find(&elements).Error; err != nil {
		logger.Error("Failed to get template elements: %v", err)
		return nil, errors.New("获取模板元素失败")
	}

	return elements, nil
}

// CreateTemplateElement 创建模板元素
func (s *templateService) CreateTemplateElement(req *CreateTemplateElementRequest) (*model.TemplateElement, error) {
	// 检查模板是否存在
	var template model.Template
	if err := s.db.Where("id = ?", req.TemplateID).First(&template).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("模板不存在")
		}
		return nil, errors.New("查询模板失败")
	}

	// 创建模板元素
	element := model.TemplateElement{
		TemplateID:   req.TemplateID,
		Type:         req.Type,
		Name:         req.Name,
		X:            req.X,
		Y:            req.Y,
		Width:        req.Width,
		Height:       req.Height,
		FontSize:     req.FontSize,
		FontWeight:   req.FontWeight,
		Color:        req.Color,
		Align:        req.Align,
		DataField:    req.DataField,
		DefaultValue: req.DefaultValue,
		Format:       req.Format,
		Sort:         req.Sort,
		Config:       req.Config,
	}

	// 设置默认值
	if element.FontSize == 0 {
		element.FontSize = 12
	}
	if element.FontWeight == "" {
		element.FontWeight = "normal"
	}
	if element.Color == "" {
		element.Color = "#000000"
	}
	if element.Align == "" {
		element.Align = "left"
	}

	if err := s.db.Create(&element).Error; err != nil {
		logger.Error("Failed to create template element: %v", err)
		return nil, errors.New("创建模板元素失败")
	}

	return &element, nil
}

// UpdateTemplateElement 更新模板元素
func (s *templateService) UpdateTemplateElement(id uint, req *UpdateTemplateElementRequest) error {
	// 检查元素是否存在
	var element model.TemplateElement
	if err := s.db.Where("id = ?", id).First(&element).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("模板元素不存在")
		}
		return errors.New("查询模板元素失败")
	}

	// 更新元素信息
	updates := map[string]interface{}{}
	if req.Type != "" {
		updates["type"] = req.Type
	}
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.X != nil {
		updates["x"] = *req.X
	}
	if req.Y != nil {
		updates["y"] = *req.Y
	}
	if req.Width != nil {
		updates["width"] = *req.Width
	}
	if req.Height != nil {
		updates["height"] = *req.Height
	}
	if req.FontSize != nil {
		updates["font_size"] = *req.FontSize
	}
	if req.FontWeight != "" {
		updates["font_weight"] = req.FontWeight
	}
	if req.Color != "" {
		updates["color"] = req.Color
	}
	if req.Align != "" {
		updates["align"] = req.Align
	}
	if req.DataField != "" {
		updates["data_field"] = req.DataField
	}
	if req.DefaultValue != "" {
		updates["default_value"] = req.DefaultValue
	}
	if req.Format != "" {
		updates["format"] = req.Format
	}
	if req.Sort != nil {
		updates["sort"] = *req.Sort
	}
	if req.Config != "" {
		updates["config"] = req.Config
	}

	if len(updates) > 0 {
		if err := s.db.Model(&element).Updates(updates).Error; err != nil {
			logger.Error("Failed to update template element: %v", err)
			return errors.New("更新模板元素失败")
		}
	}

	return nil
}

// DeleteTemplateElement 删除模板元素
func (s *templateService) DeleteTemplateElement(id uint) error {
	// 检查元素是否存在
	var element model.TemplateElement
	if err := s.db.Where("id = ?", id).First(&element).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("模板元素不存在")
		}
		return errors.New("查询模板元素失败")
	}

	// 软删除元素
	if err := s.db.Delete(&element).Error; err != nil {
		logger.Error("Failed to delete template element: %v", err)
		return errors.New("删除模板元素失败")
	}

	return nil
}

// RenderTemplate 渲染模板
func (s *templateService) RenderTemplate(templateID uint, data map[string]interface{}) ([]byte, error) {
	// 获取模板信息
	template, err := s.GetTemplateByID(templateID)
	if err != nil {
		return nil, err
	}

	// 构建渲染数据
	renderData := map[string]interface{}{
		"template": template,
		"data":     data,
	}

	// 转换为JSON
	jsonData, err := json.Marshal(renderData)
	if err != nil {
		logger.Error("Failed to marshal render data: %v", err)
		return nil, errors.New("渲染数据序列化失败")
	}

	return jsonData, nil
}

// GenerateEPD 生成EPD二进制数据
func (s *templateService) GenerateEPD(templateID uint, data map[string]interface{}) ([]byte, error) {
	// 获取模板信息
	template, err := s.GetTemplateByID(templateID)
	if err != nil {
		return nil, err
	}

	// TODO: 实现EPD二进制生成逻辑
	// 这里应该根据模板和数据生成电子墨水屏的二进制数据
	// 简化实现，返回模拟数据
	epdData := make([]byte, (template.Width*template.Height)/8)
	
	logger.Info("Generated EPD data for template %d, size: %d bytes", templateID, len(epdData))
	
	return epdData, nil
}
