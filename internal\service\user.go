package service

import (
	"electronic-price-tag/internal/middleware"
	"electronic-price-tag/internal/model"
	"electronic-price-tag/pkg/logger"
	"errors"
	"fmt"

	"github.com/casbin/casbin/v2"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type UserService interface {
	GetUsers(merchantID string, page, pageSize int, keyword string) (*UserListResponse, error)
	GetUserByID(id uint) (*model.User, error)
	CreateUser(req *CreateUserRequest) (*model.User, error)
	UpdateUser(id uint, req *UpdateUserRequest) error
	DeleteUser(id uint) error
	GetUserRoles(userID uint) ([]model.Role, error)
	AssignRoles(userID uint, roleIDs []uint) error
	GetUserPermissions(userID uint) ([]model.Permission, error)
}

type userService struct {
	db       *gorm.DB
	rdb      *redis.Client
	enforcer *casbin.Enforcer
}

type UserListResponse struct {
	Total int           `json:"total"`
	List  []model.User  `json:"list"`
}

type CreateUserRequest struct {
	Username   string `json:"username" binding:"required"`
	Password   string `json:"password" binding:"required,min=6"`
	Email      string `json:"email" binding:"email"`
	Phone      string `json:"phone"`
	RealName   string `json:"real_name"`
	MerchantID string `json:"merchant_id" binding:"required"`
	RoleIDs    []uint `json:"role_ids"`
}

type UpdateUserRequest struct {
	Email    string `json:"email" binding:"email"`
	Phone    string `json:"phone"`
	RealName string `json:"real_name"`
	Status   *int   `json:"status"`
	RoleIDs  []uint `json:"role_ids"`
}

func NewUserService(db *gorm.DB, rdb *redis.Client, enforcer *casbin.Enforcer) UserService {
	return &userService{
		db:       db,
		rdb:      rdb,
		enforcer: enforcer,
	}
}

// GetUsers 获取用户列表
func (s *userService) GetUsers(merchantID string, page, pageSize int, keyword string) (*UserListResponse, error) {
	var users []model.User
	var total int64

	query := s.db.Model(&model.User{}).Preload("Roles").Preload("Merchant")

	// 商户过滤
	if merchantID != "" {
		query = query.Where("merchant_id = ?", merchantID)
	}

	// 关键词搜索
	if keyword != "" {
		query = query.Where("username LIKE ? OR real_name LIKE ? OR email LIKE ?", 
			fmt.Sprintf("%%%s%%", keyword),
			fmt.Sprintf("%%%s%%", keyword),
			fmt.Sprintf("%%%s%%", keyword))
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count users: %v", err)
		return nil, errors.New("获取用户总数失败")
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&users).Error; err != nil {
		logger.Error("Failed to get users: %v", err)
		return nil, errors.New("获取用户列表失败")
	}

	// 清除密码字段
	for i := range users {
		users[i].Password = ""
	}

	return &UserListResponse{
		Total: int(total),
		List:  users,
	}, nil
}

// GetUserByID 根据ID获取用户
func (s *userService) GetUserByID(id uint) (*model.User, error) {
	var user model.User
	if err := s.db.Preload("Roles").Preload("Merchant").Where("id = ?", id).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("用户不存在")
		}
		logger.Error("Failed to get user by id: %v", err)
		return nil, errors.New("获取用户信息失败")
	}

	// 清除密码字段
	user.Password = ""

	return &user, nil
}

// CreateUser 创建用户
func (s *userService) CreateUser(req *CreateUserRequest) (*model.User, error) {
	// 检查用户名是否已存在
	var existUser model.User
	if err := s.db.Where("username = ?", req.Username).First(&existUser).Error; err == nil {
		return nil, errors.New("用户名已存在")
	}

	// 检查邮箱是否已存在
	if req.Email != "" {
		if err := s.db.Where("email = ?", req.Email).First(&existUser).Error; err == nil {
			return nil, errors.New("邮箱已存在")
		}
	}

	// 加密密码
	hashedPassword, err := HashPassword(req.Password)
	if err != nil {
		return nil, errors.New("密码加密失败")
	}

	// 创建用户
	user := model.User{
		Username:   req.Username,
		Password:   hashedPassword,
		Email:      req.Email,
		Phone:      req.Phone,
		RealName:   req.RealName,
		MerchantID: req.MerchantID,
		Status:     1,
	}

	if err := s.db.Create(&user).Error; err != nil {
		logger.Error("Failed to create user: %v", err)
		return nil, errors.New("创建用户失败")
	}

	// 分配角色
	if len(req.RoleIDs) > 0 {
		if err := s.AssignRoles(user.ID, req.RoleIDs); err != nil {
			logger.Error("Failed to assign roles: %v", err)
		}
	}

	// 重新查询用户信息（包含角色）
	if err := s.db.Preload("Roles").Preload("Merchant").Where("id = ?", user.ID).First(&user).Error; err != nil {
		logger.Error("Failed to reload user: %v", err)
	}

	// 清除密码字段
	user.Password = ""

	return &user, nil
}

// UpdateUser 更新用户
func (s *userService) UpdateUser(id uint, req *UpdateUserRequest) error {
	// 检查用户是否存在
	var user model.User
	if err := s.db.Where("id = ?", id).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("用户不存在")
		}
		return errors.New("查询用户失败")
	}

	// 检查邮箱是否已被其他用户使用
	if req.Email != "" && req.Email != user.Email {
		var existUser model.User
		if err := s.db.Where("email = ? AND id != ?", req.Email, id).First(&existUser).Error; err == nil {
			return errors.New("邮箱已被其他用户使用")
		}
	}

	// 更新用户信息
	updates := map[string]interface{}{}
	if req.Email != "" {
		updates["email"] = req.Email
	}
	if req.Phone != "" {
		updates["phone"] = req.Phone
	}
	if req.RealName != "" {
		updates["real_name"] = req.RealName
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	if len(updates) > 0 {
		if err := s.db.Model(&user).Updates(updates).Error; err != nil {
			logger.Error("Failed to update user: %v", err)
			return errors.New("更新用户失败")
		}
	}

	// 更新角色
	if req.RoleIDs != nil {
		if err := s.AssignRoles(id, req.RoleIDs); err != nil {
			return err
		}
	}

	return nil
}

// DeleteUser 删除用户
func (s *userService) DeleteUser(id uint) error {
	// 检查用户是否存在
	var user model.User
	if err := s.db.Where("id = ?", id).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("用户不存在")
		}
		return errors.New("查询用户失败")
	}

	// 软删除用户
	if err := s.db.Delete(&user).Error; err != nil {
		logger.Error("Failed to delete user: %v", err)
		return errors.New("删除用户失败")
	}

	// 删除用户角色关联
	if err := s.db.Where("user_id = ?", id).Delete(&model.UserRole{}).Error; err != nil {
		logger.Error("Failed to delete user roles: %v", err)
	}

	// 删除Casbin中的用户权限
	s.enforcer.DeleteUser(user.Username)

	return nil
}

// GetUserRoles 获取用户角色
func (s *userService) GetUserRoles(userID uint) ([]model.Role, error) {
	var user model.User
	if err := s.db.Preload("Roles").Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("用户不存在")
		}
		return nil, errors.New("查询用户失败")
	}

	return user.Roles, nil
}

// AssignRoles 分配角色
func (s *userService) AssignRoles(userID uint, roleIDs []uint) error {
	// 获取用户信息
	var user model.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return errors.New("用户不存在")
	}

	// 清除现有角色关联
	if err := s.db.Where("user_id = ?", userID).Delete(&model.UserRole{}).Error; err != nil {
		logger.Error("Failed to clear user roles: %v", err)
		return errors.New("清除用户角色失败")
	}

	// 删除Casbin中的用户角色
	s.enforcer.DeleteRolesForUser(user.Username)

	// 添加新的角色关联
	if len(roleIDs) > 0 {
		var roles []model.Role
		if err := s.db.Where("id IN ?", roleIDs).Find(&roles).Error; err != nil {
			return errors.New("查询角色失败")
		}

		for _, role := range roles {
			// 添加数据库关联
			userRole := model.UserRole{
				UserID: userID,
				RoleID: role.ID,
			}
			if err := s.db.Create(&userRole).Error; err != nil {
				logger.Error("Failed to create user role: %v", err)
				continue
			}

			// 添加Casbin权限
			if err := middleware.AddUserRole(s.enforcer, user.Username, role.Code, user.MerchantID); err != nil {
				logger.Error("Failed to add user role in casbin: %v", err)
			}
		}
	}

	return nil
}

// GetUserPermissions 获取用户权限
func (s *userService) GetUserPermissions(userID uint) ([]model.Permission, error) {
	var user model.User
	if err := s.db.Preload("Roles.Permissions").Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("用户不存在")
		}
		return nil, errors.New("查询用户失败")
	}

	// 收集所有权限
	permissionMap := make(map[uint]model.Permission)
	for _, role := range user.Roles {
		for _, permission := range role.Permissions {
			permissionMap[permission.ID] = permission
		}
	}

	// 转换为切片
	var permissions []model.Permission
	for _, permission := range permissionMap {
		permissions = append(permissions, permission)
	}

	return permissions, nil
}
