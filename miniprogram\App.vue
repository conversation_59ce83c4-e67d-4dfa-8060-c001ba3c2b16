<template>
  <view class="app">
    <!-- 应用入口 -->
  </view>
</template>

<script>
export default {
  name: 'App',
  onLaunch: function() {
    console.log('App Launch')
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 获取系统信息
    this.getSystemInfo()
  },
  onShow: function() {
    console.log('App Show')
  },
  onHide: function() {
    console.log('App Hide')
  },
  methods: {
    // 检查登录状态
    checkLoginStatus() {
      const token = uni.getStorageSync('token')
      if (token) {
        // 验证token有效性
        this.validateToken(token)
      }
    },
    
    // 验证token
    async validateToken(token) {
      try {
        const res = await uni.request({
          url: 'http://localhost:8080/api/auth/validate',
          method: 'POST',
          header: {
            'Authorization': `Bearer ${token}`
          }
        })
        
        if (res.data.code !== 200) {
          // token无效，清除本地存储
          uni.removeStorageSync('token')
          uni.removeStorageSync('userInfo')
        }
      } catch (error) {
        console.error('Token validation failed:', error)
        uni.removeStorageSync('token')
        uni.removeStorageSync('userInfo')
      }
    },
    
    // 获取系统信息
    getSystemInfo() {
      uni.getSystemInfo({
        success: (res) => {
          console.log('System Info:', res)
          // 保存系统信息到全局
          getApp().globalData.systemInfo = res
        }
      })
    }
  },
  globalData: {
    systemInfo: null,
    userInfo: null
  }
}
</script>

<style lang="scss">
/* 全局样式 */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

.app {
  height: 100vh;
}

/* 通用样式 */
.container {
  padding: 20rpx;
}

.card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.btn-secondary {
  background: #f5f7fa;
  color: #606266;
  border: 2rpx solid #dcdfe6;
  border-radius: 12rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
}

.text-primary {
  color: #409eff;
}

.text-success {
  color: #67c23a;
}

.text-warning {
  color: #e6a23c;
}

.text-danger {
  color: #f56c6c;
}

.text-info {
  color: #909399;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.mb-20 {
  margin-bottom: 20rpx;
}

.mb-40 {
  margin-bottom: 40rpx;
}

.mt-20 {
  margin-top: 20rpx;
}

.mt-40 {
  margin-top: 40rpx;
}

.p-20 {
  padding: 20rpx;
}

.p-40 {
  padding: 40rpx;
}

/* 状态样式 */
.status-online {
  color: #67c23a;
}

.status-offline {
  color: #909399;
}

.status-error {
  color: #f56c6c;
}

/* 加载样式 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  color: #909399;
}

/* 空状态样式 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  color: #909399;
  
  .empty-icon {
    font-size: 96rpx;
    margin-bottom: 24rpx;
  }
  
  .empty-text {
    font-size: 28rpx;
  }
}
</style>
