<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子价签管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            width: 90%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .header p {
            color: #666;
            font-size: 16px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .devices {
            margin-top: 30px;
        }
        
        .devices h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        .device-list {
            display: grid;
            gap: 15px;
        }
        
        .device-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .device-info {
            flex: 1;
        }
        
        .device-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .device-sn {
            color: #666;
            font-size: 14px;
        }
        
        .device-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-online {
            background: #d4edda;
            color: #155724;
        }
        
        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        .battery {
            font-size: 14px;
            color: #666;
        }
        
        .loading {
            text-align: center;
            color: #666;
            padding: 20px;
        }
        
        .error {
            text-align: center;
            color: #dc3545;
            padding: 20px;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏷️ 电子价签管理系统</h1>
            <p>Electronic Price Tag Management System</p>
        </div>
        
        <div class="stats" id="stats">
            <div class="loading">正在加载统计数据...</div>
        </div>
        
        <div class="devices">
            <h2>📱 设备列表</h2>
            <div class="device-list" id="deviceList">
                <div class="loading">正在加载设备数据...</div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="refreshData()">🔄 刷新数据</button>
            <button class="btn" onclick="testLogin()">🔑 测试登录</button>
        </div>
    </div>

    <script>
        // API基础地址
        const API_BASE = 'http://localhost:8080/api';
        
        // 加载统计数据
        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE}/stats`);
                const data = await response.json();
                
                if (data.code === 200) {
                    const statsHtml = `
                        <div class="stat-card">
                            <div class="stat-value">${data.data.device_count}</div>
                            <div class="stat-label">设备总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${data.data.online_count}</div>
                            <div class="stat-label">在线设备</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${data.data.product_count}</div>
                            <div class="stat-label">商品总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${data.data.template_count}</div>
                            <div class="stat-label">模板总数</div>
                        </div>
                    `;
                    document.getElementById('stats').innerHTML = statsHtml;
                } else {
                    throw new Error(data.msg);
                }
            } catch (error) {
                document.getElementById('stats').innerHTML = `<div class="error">加载统计数据失败: ${error.message}</div>`;
            }
        }
        
        // 加载设备列表
        async function loadDevices() {
            try {
                const response = await fetch(`${API_BASE}/devices`);
                const data = await response.json();
                
                if (data.code === 200) {
                    const devices = data.data.list;
                    const devicesHtml = devices.map(device => `
                        <div class="device-item">
                            <div class="device-info">
                                <div class="device-name">${device.name}</div>
                                <div class="device-sn">SN: ${device.sn}</div>
                            </div>
                            <div class="device-status">
                                <span class="status-badge ${device.status === 1 ? 'status-online' : 'status-offline'}">
                                    ${device.status === 1 ? '在线' : '离线'}
                                </span>
                                <span class="battery">🔋 ${device.battery}%</span>
                            </div>
                        </div>
                    `).join('');
                    
                    document.getElementById('deviceList').innerHTML = devicesHtml;
                } else {
                    throw new Error(data.msg);
                }
            } catch (error) {
                document.getElementById('deviceList').innerHTML = `<div class="error">加载设备数据失败: ${error.message}</div>`;
            }
        }
        
        // 测试登录
        async function testLogin() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: '123456'
                    })
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    alert(`登录成功！\n用户: ${data.data.user.real_name}\nToken: ${data.data.token.substring(0, 20)}...`);
                } else {
                    alert(`登录失败: ${data.msg}`);
                }
            } catch (error) {
                alert(`登录失败: ${error.message}`);
            }
        }
        
        // 刷新数据
        function refreshData() {
            loadStats();
            loadDevices();
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadDevices();
        });
    </script>
</body>
</html>
