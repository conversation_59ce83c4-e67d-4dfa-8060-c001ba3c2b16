# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build directory
build/
tmp/

# Logs
logs/
*.log

# Configuration files with sensitive data
configs/local.yaml
configs/production.yaml
.env
.env.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Storage and upload directories
storage/
uploads/
static/uploads/

# Test coverage
coverage.out
coverage.html

# Air live reload
build-errors.log

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
*.tmp
*.temp

# Documentation
docs/

# Node modules (if any frontend build tools)
node_modules/

# Docker volumes
mysql_data/
redis_data/
emqx_data/
emqx_log/
prometheus_data/
grafana_data/

# Certificates and keys
*.pem
*.key
*.crt
*.p12

# Backup files
*.bak
*.backup

# Cache directories
.cache/
cache/

# Firmware files
firmware/
*.bin
*.hex

# Generated files
*.pb.go
*_gen.go
