{"name": "electronic-price-tag-frontend", "version": "1.0.0", "description": "电子价签系统前端管理界面", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "element-plus": "^2.3.8", "axios": "^1.4.0", "@element-plus/icons-vue": "^2.1.0", "echarts": "^5.4.3", "vue-echarts": "^6.6.0", "fabric": "^5.3.0", "dayjs": "^1.11.9", "nprogress": "^0.2.0", "js-cookie": "^3.0.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "prettier": "^3.0.0", "@types/js-cookie": "^3.0.3", "@types/nprogress": "^0.2.0", "sass": "^1.64.1", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}}