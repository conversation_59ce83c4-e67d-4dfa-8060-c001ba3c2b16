package utils

import (
	"time"
)

// GetCurrentTimestamp 获取当前时间戳（秒）
func GetCurrentTimestamp() int64 {
	return time.Now().Unix()
}

// GetCurrentTimestampMs 获取当前时间戳（毫秒）
func GetCurrentTimestampMs() int64 {
	return time.Now().UnixNano() / int64(time.Millisecond)
}

// GetCurrentTime 获取当前时间
func GetCurrentTime() time.Time {
	return time.Now()
}

// GetCurrentTimeString 获取当前时间字符串
func GetCurrentTimeString() string {
	return time.Now().Format("2006-01-02 15:04:05")
}

// GetCurrentDateString 获取当前日期字符串
func GetCurrentDateString() string {
	return time.Now().Format("2006-01-02")
}

// ParseTime 解析时间字符串
func ParseTime(timeStr string) (time.Time, error) {
	layouts := []string{
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
		"2006-01-02",
		"15:04:05",
	}
	
	for _, layout := range layouts {
		if t, err := time.Parse(layout, timeStr); err == nil {
			return t, nil
		}
	}
	
	return time.Time{}, nil
}

// FormatTime 格式化时间
func FormatTime(t time.Time, layout string) string {
	if layout == "" {
		layout = "2006-01-02 15:04:05"
	}
	return t.Format(layout)
}

// TimestampToTime 时间戳转时间
func TimestampToTime(timestamp int64) time.Time {
	return time.Unix(timestamp, 0)
}

// TimeToTimestamp 时间转时间戳
func TimeToTimestamp(t time.Time) int64 {
	return t.Unix()
}

// GetStartOfDay 获取一天的开始时间
func GetStartOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

// GetEndOfDay 获取一天的结束时间
func GetEndOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 999999999, t.Location())
}

// GetStartOfWeek 获取一周的开始时间（周一）
func GetStartOfWeek(t time.Time) time.Time {
	weekday := int(t.Weekday())
	if weekday == 0 {
		weekday = 7 // 将周日调整为7
	}
	return GetStartOfDay(t.AddDate(0, 0, -(weekday-1)))
}

// GetEndOfWeek 获取一周的结束时间（周日）
func GetEndOfWeek(t time.Time) time.Time {
	return GetEndOfDay(GetStartOfWeek(t).AddDate(0, 0, 6))
}

// GetStartOfMonth 获取一个月的开始时间
func GetStartOfMonth(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), 1, 0, 0, 0, 0, t.Location())
}

// GetEndOfMonth 获取一个月的结束时间
func GetEndOfMonth(t time.Time) time.Time {
	return GetStartOfMonth(t).AddDate(0, 1, 0).Add(-time.Nanosecond)
}

// GetStartOfYear 获取一年的开始时间
func GetStartOfYear(t time.Time) time.Time {
	return time.Date(t.Year(), 1, 1, 0, 0, 0, 0, t.Location())
}

// GetEndOfYear 获取一年的结束时间
func GetEndOfYear(t time.Time) time.Time {
	return GetStartOfYear(t).AddDate(1, 0, 0).Add(-time.Nanosecond)
}

// IsToday 判断是否为今天
func IsToday(t time.Time) bool {
	now := time.Now()
	return t.Year() == now.Year() && t.Month() == now.Month() && t.Day() == now.Day()
}

// IsYesterday 判断是否为昨天
func IsYesterday(t time.Time) bool {
	yesterday := time.Now().AddDate(0, 0, -1)
	return t.Year() == yesterday.Year() && t.Month() == yesterday.Month() && t.Day() == yesterday.Day()
}

// DaysBetween 计算两个日期之间的天数
func DaysBetween(start, end time.Time) int {
	if start.After(end) {
		start, end = end, start
	}
	
	startDay := GetStartOfDay(start)
	endDay := GetStartOfDay(end)
	
	return int(endDay.Sub(startDay).Hours() / 24)
}

// HumanizeTime 人性化时间显示
func HumanizeTime(t time.Time) string {
	now := time.Now()
	diff := now.Sub(t)
	
	if diff < time.Minute {
		return "刚刚"
	} else if diff < time.Hour {
		return fmt.Sprintf("%d分钟前", int(diff.Minutes()))
	} else if diff < 24*time.Hour {
		return fmt.Sprintf("%d小时前", int(diff.Hours()))
	} else if diff < 7*24*time.Hour {
		return fmt.Sprintf("%d天前", int(diff.Hours()/24))
	} else if diff < 30*24*time.Hour {
		return fmt.Sprintf("%d周前", int(diff.Hours()/(24*7)))
	} else if diff < 365*24*time.Hour {
		return fmt.Sprintf("%d个月前", int(diff.Hours()/(24*30)))
	} else {
		return fmt.Sprintf("%d年前", int(diff.Hours()/(24*365)))
	}
}

// GetTimeZone 获取时区
func GetTimeZone() string {
	_, offset := time.Now().Zone()
	return fmt.Sprintf("UTC%+d", offset/3600)
}

// ConvertTimeZone 转换时区
func ConvertTimeZone(t time.Time, timezone string) (time.Time, error) {
	loc, err := time.LoadLocation(timezone)
	if err != nil {
		return t, err
	}
	return t.In(loc), nil
}

// GetWeekday 获取星期几（中文）
func GetWeekday(t time.Time) string {
	weekdays := []string{"周日", "周一", "周二", "周三", "周四", "周五", "周六"}
	return weekdays[t.Weekday()]
}

// IsWeekend 判断是否为周末
func IsWeekend(t time.Time) bool {
	weekday := t.Weekday()
	return weekday == time.Saturday || weekday == time.Sunday
}

// IsWorkday 判断是否为工作日
func IsWorkday(t time.Time) bool {
	return !IsWeekend(t)
}

// GetQuarter 获取季度
func GetQuarter(t time.Time) int {
	month := int(t.Month())
	return (month-1)/3 + 1
}

// GetStartOfQuarter 获取季度开始时间
func GetStartOfQuarter(t time.Time) time.Time {
	quarter := GetQuarter(t)
	month := (quarter-1)*3 + 1
	return time.Date(t.Year(), time.Month(month), 1, 0, 0, 0, 0, t.Location())
}

// GetEndOfQuarter 获取季度结束时间
func GetEndOfQuarter(t time.Time) time.Time {
	return GetStartOfQuarter(t).AddDate(0, 3, 0).Add(-time.Nanosecond)
}
