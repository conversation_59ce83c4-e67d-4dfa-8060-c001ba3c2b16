package main

import (
	"context"
	"electronic-price-tag/internal/config"
	"electronic-price-tag/internal/database"
	"electronic-price-tag/internal/middleware"
	"electronic-price-tag/internal/router"
	"electronic-price-tag/internal/service"
	"electronic-price-tag/pkg/logger"
	"electronic-price-tag/pkg/mqtt"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
)

func main() {
	// 初始化配置
	cfg := config.Load()
	
	// 初始化日志
	logger.Init(cfg.Log.Level)
	
	// 初始化数据库
	db, err := database.Init(cfg.Database)
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	
	// 初始化Redis
	rdb, err := database.InitRedis(cfg.Redis)
	if err != nil {
		log.Fatal("Failed to initialize Redis:", err)
	}
	
	// 初始化权限系统
	enforcer, err := middleware.InitCasbin(db)
	if err != nil {
		log.Fatal("Failed to initialize Casbin:", err)
	}
	
	// 初始化MQTT客户端
	mqttClient, err := mqtt.NewClient(cfg.MQTT)
	if err != nil {
		log.Fatal("Failed to initialize MQTT:", err)
	}
	
	// 初始化服务层
	services := service.NewServices(db, rdb, mqttClient, enforcer)
	
	// 设置Gin模式
	if cfg.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}
	
	// 创建路由
	r := router.SetupRouter(services, enforcer)
	
	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: r,
	}
	
	// 启动服务器
	go func() {
		logger.Info("Server starting on port %d", cfg.Server.Port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()
	
	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	logger.Info("Shutting down server...")
	
	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}
	
	// 关闭MQTT连接
	mqttClient.Disconnect(250)
	
	logger.Info("Server exited")
}
