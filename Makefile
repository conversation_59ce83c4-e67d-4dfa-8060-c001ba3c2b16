# 项目名称
PROJECT_NAME := electronic-price-tag
BINARY_NAME := electronic-price-tag-server

# Go 相关变量
GO := go
GOMOD := $(GO) mod
GOBUILD := $(GO) build
GOCLEAN := $(GO) clean
GOTEST := $(GO) test
GOGET := $(GO) get
GORUN := $(GO) run

# 构建相关变量
BUILD_DIR := build
MAIN_PATH := cmd/server/main.go
LDFLAGS := -ldflags "-X main.Version=$(shell git describe --tags --always --dirty) -X main.BuildTime=$(shell date +%Y-%m-%d_%H:%M:%S)"

# 默认目标
.PHONY: all
all: clean deps build

# 安装依赖
.PHONY: deps
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# 构建
.PHONY: build
build:
	mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)

# 构建 Linux 版本
.PHONY: build-linux
build-linux:
	mkdir -p $(BUILD_DIR)
	GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 $(MAIN_PATH)

# 构建 Windows 版本
.PHONY: build-windows
build-windows:
	mkdir -p $(BUILD_DIR)
	GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe $(MAIN_PATH)

# 构建 macOS 版本
.PHONY: build-darwin
build-darwin:
	mkdir -p $(BUILD_DIR)
	GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-amd64 $(MAIN_PATH)

# 构建所有平台版本
.PHONY: build-all
build-all: build-linux build-windows build-darwin

# 运行
.PHONY: run
run:
	$(GORUN) $(MAIN_PATH)

# 开发模式运行（带热重载）
.PHONY: dev
dev:
	air -c .air.toml

# 测试
.PHONY: test
test:
	$(GOTEST) -v ./...

# 测试覆盖率
.PHONY: test-coverage
test-coverage:
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GO) tool cover -html=coverage.out -o coverage.html

# 基准测试
.PHONY: bench
bench:
	$(GOTEST) -bench=. -benchmem ./...

# 代码格式化
.PHONY: fmt
fmt:
	$(GO) fmt ./...

# 代码检查
.PHONY: vet
vet:
	$(GO) vet ./...

# 静态分析
.PHONY: lint
lint:
	golangci-lint run

# 生成 Swagger 文档
.PHONY: swagger
swagger:
	swag init -g $(MAIN_PATH) -o ./docs

# 清理
.PHONY: clean
clean:
	$(GOCLEAN)
	rm -rf $(BUILD_DIR)
	rm -f coverage.out coverage.html

# 安装工具
.PHONY: install-tools
install-tools:
	$(GOGET) -u github.com/cosmtrek/air
	$(GOGET) -u github.com/swaggo/swag/cmd/swag
	$(GOGET) -u github.com/golangci/golangci-lint/cmd/golangci-lint

# Docker 相关
.PHONY: docker-build
docker-build:
	docker build -t $(PROJECT_NAME):latest .

.PHONY: docker-run
docker-run:
	docker run -p 8080:8080 --name $(PROJECT_NAME) $(PROJECT_NAME):latest

.PHONY: docker-stop
docker-stop:
	docker stop $(PROJECT_NAME)
	docker rm $(PROJECT_NAME)

# 数据库迁移
.PHONY: migrate-up
migrate-up:
	migrate -path ./migrations -database "mysql://root:123456@tcp(localhost:3307)/electronic_price_tag" up

.PHONY: migrate-down
migrate-down:
	migrate -path ./migrations -database "mysql://root:123456@tcp(localhost:3307)/electronic_price_tag" down

.PHONY: migrate-create
migrate-create:
	migrate create -ext sql -dir ./migrations -seq $(name)

# 生成模拟数据
.PHONY: seed
seed:
	$(GORUN) scripts/seed.go

# 启动服务
.PHONY: start
start: build
	./$(BUILD_DIR)/$(BINARY_NAME)

# 重启服务
.PHONY: restart
restart: stop start

# 停止服务
.PHONY: stop
stop:
	pkill -f $(BINARY_NAME) || true

# 检查服务状态
.PHONY: status
status:
	pgrep -f $(BINARY_NAME) && echo "Service is running" || echo "Service is not running"

# 查看日志
.PHONY: logs
logs:
	tail -f logs/app.log

# 帮助
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  all          - Clean, install deps and build"
	@echo "  deps         - Install dependencies"
	@echo "  build        - Build the application"
	@echo "  build-all    - Build for all platforms"
	@echo "  run          - Run the application"
	@echo "  dev          - Run in development mode with hot reload"
	@echo "  test         - Run tests"
	@echo "  test-coverage- Run tests with coverage"
	@echo "  bench        - Run benchmarks"
	@echo "  fmt          - Format code"
	@echo "  vet          - Run go vet"
	@echo "  lint         - Run linter"
	@echo "  swagger      - Generate Swagger docs"
	@echo "  clean        - Clean build artifacts"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"
	@echo "  start        - Start the service"
	@echo "  stop         - Stop the service"
	@echo "  restart      - Restart the service"
	@echo "  status       - Check service status"
	@echo "  help         - Show this help"
