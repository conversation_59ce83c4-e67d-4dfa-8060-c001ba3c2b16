package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"fmt"
	"io"

	"golang.org/x/crypto/bcrypt"
)

// HashPassword 使用bcrypt加密密码
func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

// CheckPassword 验证密码
func CheckPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// MD5 计算MD5哈希
func MD5(data string) string {
	hash := md5.Sum([]byte(data))
	return hex.EncodeToString(hash[:])
}

// SHA256 计算SHA256哈希
func SHA256(data string) string {
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

// GenerateRandomString 生成随机字符串
func GenerateRandomString(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(bytes)[:length], nil
}

// GenerateRandomBytes 生成随机字节
func GenerateRandomBytes(length int) ([]byte, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return nil, err
	}
	return bytes, nil
}

// AESEncrypt AES加密
func AESEncrypt(plaintext, key string) (string, error) {
	// 创建cipher.Block接口
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	// 填充原文
	plaintextBytes := PKCS7Padding([]byte(plaintext), aes.BlockSize)

	// 创建加密模式
	ciphertext := make([]byte, aes.BlockSize+len(plaintextBytes))
	iv := ciphertext[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", err
	}

	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext[aes.BlockSize:], plaintextBytes)

	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// AESDecrypt AES解密
func AESDecrypt(ciphertext, key string) (string, error) {
	// 解码base64
	ciphertextBytes, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	// 创建cipher.Block接口
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	// 检查密文长度
	if len(ciphertextBytes) < aes.BlockSize {
		return "", errors.New("ciphertext too short")
	}

	// 提取IV
	iv := ciphertextBytes[:aes.BlockSize]
	ciphertextBytes = ciphertextBytes[aes.BlockSize:]

	// 检查密文长度是否为块大小的倍数
	if len(ciphertextBytes)%aes.BlockSize != 0 {
		return "", errors.New("ciphertext is not a multiple of the block size")
	}

	// 创建解密模式
	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(ciphertextBytes, ciphertextBytes)

	// 去除填充
	plaintext := PKCS7UnPadding(ciphertextBytes)
	return string(plaintext), nil
}

// PKCS7Padding PKCS7填充
func PKCS7Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := make([]byte, padding)
	for i := range padtext {
		padtext[i] = byte(padding)
	}
	return append(ciphertext, padtext...)
}

// PKCS7UnPadding PKCS7去填充
func PKCS7UnPadding(origData []byte) []byte {
	length := len(origData)
	if length == 0 {
		return origData
	}
	unpadding := int(origData[length-1])
	if unpadding > length {
		return origData
	}
	return origData[:(length - unpadding)]
}

// GenerateAESKey 生成AES密钥
func GenerateAESKey(length int) (string, error) {
	if length != 16 && length != 24 && length != 32 {
		return "", errors.New("key length must be 16, 24, or 32 bytes")
	}
	
	key := make([]byte, length)
	if _, err := rand.Read(key); err != nil {
		return "", err
	}
	
	return hex.EncodeToString(key), nil
}

// SimpleEncrypt 简单加密（用于非敏感数据）
func SimpleEncrypt(data, key string) string {
	keyBytes := []byte(key)
	dataBytes := []byte(data)
	
	result := make([]byte, len(dataBytes))
	for i, b := range dataBytes {
		result[i] = b ^ keyBytes[i%len(keyBytes)]
	}
	
	return base64.StdEncoding.EncodeToString(result)
}

// SimpleDecrypt 简单解密
func SimpleDecrypt(data, key string) (string, error) {
	dataBytes, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		return "", err
	}
	
	keyBytes := []byte(key)
	result := make([]byte, len(dataBytes))
	for i, b := range dataBytes {
		result[i] = b ^ keyBytes[i%len(keyBytes)]
	}
	
	return string(result), nil
}

// GenerateDeviceSecret 生成设备密钥
func GenerateDeviceSecret(deviceSN string) string {
	// 使用设备SN和时间戳生成密钥
	data := fmt.Sprintf("%s:%d", deviceSN, GetCurrentTimestamp())
	return SHA256(data)[:32] // 取前32位作为密钥
}

// ValidateDeviceSecret 验证设备密钥
func ValidateDeviceSecret(deviceSN, secret string) bool {
	// 这里可以实现更复杂的验证逻辑
	// 简单实现：检查密钥长度和格式
	if len(secret) != 32 {
		return false
	}
	
	// 检查是否为有效的十六进制字符串
	_, err := hex.DecodeString(secret)
	return err == nil
}
