server:
  port: 8080
  mode: debug

database:
  host: localhost
  port: 3307
  username: root
  password: "123456"
  database: electronic_price_tag
  charset: utf8mb4

redis:
  host: localhost
  port: 6379
  password: ""
  db: 0

jwt:
  secret: "electronic-price-tag-secret-key-2023"
  expire_time: 86400 # 24 hours

mqtt:
  broker: "tcp://localhost:1883"
  username: ""
  password: ""
  client_id: "electronic-price-tag-server"

log:
  level: info

ota:
  secret_key: "ota-secret-key-2023"
  store_path: "./storage/firmware"
