import request from '@/utils/request'

// 获取模板列表
export function getTemplates(params) {
  return request({
    url: '/templates',
    method: 'get',
    params
  })
}

// 获取模板详情
export function getTemplate(id) {
  return request({
    url: `/templates/${id}`,
    method: 'get'
  })
}

// 创建模板
export function createTemplate(data) {
  return request({
    url: '/templates',
    method: 'post',
    data
  })
}

// 更新模板
export function updateTemplate(id, data) {
  return request({
    url: `/templates/${id}`,
    method: 'put',
    data
  })
}

// 删除模板
export function deleteTemplate(id) {
  return request({
    url: `/templates/${id}`,
    method: 'delete'
  })
}

// 获取模板元素
export function getTemplateElements(id) {
  return request({
    url: `/templates/${id}/elements`,
    method: 'get'
  })
}

// 创建模板元素
export function createTemplateElement(id, data) {
  return request({
    url: `/templates/${id}/elements`,
    method: 'post',
    data
  })
}

// 更新模板元素
export function updateTemplateElement(id, data) {
  return request({
    url: `/templates/elements/${id}`,
    method: 'put',
    data
  })
}

// 删除模板元素
export function deleteTemplateElement(id) {
  return request({
    url: `/templates/elements/${id}`,
    method: 'delete'
  })
}

// 渲染模板
export function renderTemplate(id, data) {
  return request({
    url: `/templates/${id}/render`,
    method: 'post',
    data
  })
}

// 生成EPD数据
export function generateEPD(id, data) {
  return request({
    url: `/templates/${id}/generate-epd`,
    method: 'post',
    data
  })
}
