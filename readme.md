## 电子价签系统

## 技术栈
1.后端使用 Go + Gim +  gRPC +  Casbin + JWT + MQTT 5.0 + EMQX实现
2.前端使用 Vue + ElementUI + vite + ECharts实现
3.小程序使用uniapp实现
4.数据库使用MySQL + redis实现，mysql用户名root 密码123456 端口3307

## 核心功能
1. 增强型RBAC权限系统
```go
// 权限模型定义
type Policy struct {
    Subject string // 用户/角色
    Domain  string // 商户ID
    Object  string // 资源路径
    Action  string // 操作方法
}

// 动态权限拦截器
func AuthInterceptor(c *gin.Context) {
    // 从JWT解析用户权限
    claims := c.MustGet("claims").(*CustomClaims)
    
    // 构造访问三元组
    obj := c.Request.URL.Path
    act := c.Request.Method
    domain := c.Query("merchant_id")
    
    // 调用Casbin验证
    if ok, _ := enforcer.Enforce(claims.Subject, domain, obj, act); !ok {
        c.AbortWithStatusJSON(403, gin.H{"error": "权限不足"})
        return
    }
    c.Next()
}

// 数据权限SQL改写
func DataScopeFilter(uid string) func(db *gorm.DB) *gorm.DB {
    return func(db *gorm.DB) *gorm.DB {
        // 根据用户权限动态拼接WHERE条件
        if !isAdmin(uid) {
            return db.Where("merchant_id IN (?)", getUserMerchants(uid))
        }
        return db
    }
}
```

#### 2. 智能设备管理中台
```mermaid
sequenceDiagram
    设备->>基站: MQTT Connect(携带SN号)
    基站->>认证服务: 设备身份验证
    认证服务-->>基站: 返回设备密钥
    基站->>设备: 建立安全通道
    设备->>基站: 定时上报状态(心跳)
    基站->>时序数据库: 存储设备状态
    基站->>消息队列: 发布状态变更事件
    业务服务->>消息队列: 订阅设备事件
```

#### 3. 动态模板引擎实现
```typescript
// 模板设计器核心逻辑
class TemplateDesigner {
  private canvas: fabric.Canvas;
  
  async init() {
    // 初始化画布（电子墨水屏分辨率）
    this.canvas = new fabric.Canvas('template-canvas', {
      width: 296,
      height: 128,
      backgroundColor: '#fff'
    });
    
    // 绑定数据源
    this.bindDataFields();
  }

  // 数据绑定配置
  private bindDataFields() {
    const fields = [
      { type: 'text', field: 'productName', options: { fontSize: 16 } },
      { type: 'price', field: 'currentPrice', options: { color: '#FF0000' } },
      { type: 'image', field: 'productImage', options: { width: 80 } }
    ];
    
    fields.forEach(f => {
      const element = this.createCanvasElement(f);
      element.set({
        dataBinding: { field: f.field, type: f.type }
      });
      this.canvas.add(element);
    });
  }

  // 生成EPD二进制
  async generateEPD() {
    const json = this.canvas.toJSON();
    const imageBuffer = await this.convertToEPD(json);
    return this.compressEPD(imageBuffer);
  }
}
```

---

### 三、关键技术实现

#### 1. 设备OTA升级优化
```go
// 差分升级服务
type OTAService struct {
    diffCache *ristretto.Cache
}

func (s *OTAService) GetUpdatePackage(deviceModel, currentVer string) ([]byte, error) {
    cacheKey := fmt.Sprintf("%s:%s", deviceModel, currentVer)
    
    // 缓存最近10个版本差分包
    if pkg, ok := s.diffCache.Get(cacheKey); ok {
        return pkg.([]byte), nil
    }
    
    latestVer := s.repo.GetLatestVersion(deviceModel)
    baseFile := s.repo.GetFirmware(currentVer)
    targetFile := s.repo.GetFirmware(latestVer)
    
    // 生成BSDIFF差分包
    diff := bsdiff.Diff(baseFile, targetFile)
    
    // 添加数字签名
    sign := hmac.New(sha256.New, config.SecretKey)
    sign.Write(diff)
    signedDiff := append(diff, sign.Sum(nil)...)
    
    s.diffCache.Set(cacheKey, signedDiff, 0)
    return signedDiff, nil
}

// 设备端验证逻辑
func verifyUpdate(pkg []byte) bool {
    if len(pkg) < 32 {
        return false
    }
    
    sig := pkg[len(pkg)-32:]
    content := pkg[:len(pkg)-32]
    
    expectedSig := hmac.New(sha256.New, deviceSecret)
    expectedSig.Write(content)
    return hmac.Equal(sig, expectedSig.Sum(nil))
}
```

#### 2. 海量设备状态监控
```sql
-- TDengine超级表定义
CREATE STABLE IF NOT EXISTS device_status (
    ts TIMESTAMP,
    status TINYINT,
    battery SMALLINT,
    rssi SMALLINT
) TAGS (
    merchant_id VARCHAR(32),
    device_sn VARCHAR(64),
    base_station VARCHAR(32)
);

-- 实时状态查询
SELECT 
    LAST(ts) AS last_seen,
    LAST(status) AS current_status,
    AVG(battery) AS avg_battery 
FROM device_status
WHERE merchant_id = 'M123456'
INTERVAL(1m)
SLIDING(30s);
```

---

### 四、安全体系设计

#### 1. 设备认证流程
```mermaid
graph TD
    A[设备上电] --> B{是否首次激活}
    B -->|是| C[生成临时证书]
    C --> D[云端验证设备SN]
    D --> E[颁发正式密钥]
    B -->|否| F[使用存储密钥认证]
    E --> G[建立安全通道]
    F --> G
    G --> H[正常通信]
```

#### 2. 动态密钥管理
```python
class KeyManager:
    def __init__(self):
        self.keystore = KeyVault()
        
    def rotate_keys(self):
        # 每日轮换设备密钥
        active_devices = Device.query.filter_by(is_online=True)
        for device in active_devices:
            new_key = os.urandom(32)
            encrypted_key = self._encrypt_with_master(new_key)
            # 通过安全通道下发新密钥
            MQTT.publish(f"device/{device.id}/key_update", encrypted_key)
            self.keystore.update(device.id, new_key)
            
    def _encrypt_with_master(self, data):
        cipher = AES.new(MASTER_KEY, AES.MODE_GCM)
        ciphertext, tag = cipher.encrypt_and_digest(data)
        return cipher.nonce + tag + ciphertext
```

---

### 五、部署架构建议

```
边缘计算层（部署在商场本地）
├── 轻量级MQTT Broker
├── 本地缓存服务（Redis）
├── 离线任务队列
└── 断网应急处理模块

云端核心服务
├── 设备认证中心
├── 模板渲染集群
├── 分布式OTA服务
├── 时序数据库集群
└── 智能分析平台
```

---

### 六、扩展能力设计

#### 1. 多硬件兼容层
```c
// 统一设备驱动接口
typedef struct {
    int (*init)(DeviceConfig* config);
    int (*display)(const uint8_t* epd_data);
    int (*get_battery)();
    int (*deep_sleep)();
} DeviceDriver;

// 驱动注册表
static struct {
    const char* model;
    DeviceDriver driver;
} driver_registry[] = {
    { "EINK_2.9", eink_29_driver },
    { "LCD_4.3", lcd_43_driver },
    { "LED_128x64", led_128x64_driver }
};

// 统一调用入口
int device_display(const char* model, const uint8_t* data) {
    for (int i = 0; i < sizeof(driver_registry)/sizeof(*driver_registry); i++) {
        if (strcmp(model, driver_registry[i].model) == 0) {
            return driver_registry[i].driver.display(data);
        }
    }
    return -1; // 不支持的设备型号
}
```

#### 2. 智能数据分析
```python
class PriceOptimizer:
    def optimize(self, product_id):
        # 获取历史销售数据
        sales_data = DataWarehouse.get_sales(product_id)
        # 获取竞品价格
        competitors = PriceCrawler.get_competitors(product_id)
        # 训练预测模型
        model = Prophet()
        model.fit(sales_data)
        # 生成价格建议
        forecast = model.predict()
        return self._calculate_optimal_price(forecast, competitors)
```

