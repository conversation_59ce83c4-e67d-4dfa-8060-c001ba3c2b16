# 电子价签管理系统

一个基于 Go + Vue.js 的智能电子价签管理系统，支持设备管理、模板设计、商品管理、OTA升级等功能。

## 🚀 项目特性

### 后端技术栈
- **Go 1.21+** - 高性能后端服务
- **Gin** - 轻量级 Web 框架
- **GORM** - ORM 数据库操作
- **MySQL 8.0** - 主数据库
- **Redis** - 缓存和会话存储
- **MQTT (EMQX)** - 设备通信协议
- **Casbin** - 权限管理
- **JWT** - 身份认证
- **gRPC** - 微服务通信

### 前端技术栈
- **Vue 3** - 渐进式前端框架
- **Element Plus** - UI 组件库
- **Vite** - 构建工具
- **Pinia** - 状态管理
- **Vue Router** - 路由管理
- **Axios** - HTTP 客户端
- **ECharts** - 数据可视化

### 核心功能
- 🏪 **多商户管理** - 支持多商户独立管理
- 📱 **设备管理** - 设备注册、状态监控、远程控制
- 🎨 **模板设计** - 可视化模板设计器
- 📦 **商品管理** - 商品信息、价格管理
- 🔄 **OTA升级** - 固件远程升级
- 👥 **用户权限** - RBAC权限控制
- 📊 **数据统计** - 实时监控面板
- 🔌 **MQTT通信** - 实时设备通信
- 📱 **小程序支持** - UniApp小程序端

## 📋 系统要求

### 开发环境
- Go 1.21+
- Node.js 16+
- MySQL 8.0+
- Redis 6.0+
- EMQX 5.0+ (MQTT Broker)

### 生产环境
- 2核4G内存以上
- 50GB+ 存储空间
- CentOS 7+ / Ubuntu 18.04+

## 🛠️ 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-repo/electronic-price-tag.git
cd electronic-price-tag
```

### 2. 后端启动

#### 方式一：本地开发
```bash
# 安装依赖
make deps

# 启动数据库和中间件 (Docker)
docker-compose up -d mysql redis emqx

# 运行数据库初始化脚本
mysql -h127.0.0.1 -P3307 -uroot -p123456 < scripts/init.sql

# 启动后端服务
make run
# 或开发模式 (热重载)
make dev
```

#### 方式二：Docker 部署
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps
```

#### 方式三：一键启动脚本
```bash
chmod +x scripts/start.sh
./scripts/start.sh start
```

### 3. 前端启动
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

### 4. 访问系统
- 后端API: http://localhost:8080
- 前端管理: http://localhost:3000
- EMQX控制台: http://localhost:18083 (admin/public)
- Grafana监控: http://localhost:3000 (admin/admin)

### 5. 默认账号
- 用户名: `admin`
- 密码: `123456`

## 📁 项目结构

```
electronic-price-tag/
├── cmd/                    # 应用入口
│   └── server/
│       └── main.go
├── internal/               # 内部包
│   ├── config/            # 配置管理
│   ├── controller/        # 控制器
│   ├── database/          # 数据库初始化
│   ├── middleware/        # 中间件
│   ├── model/             # 数据模型
│   ├── router/            # 路由配置
│   └── service/           # 业务逻辑
├── pkg/                   # 公共包
│   ├── logger/            # 日志工具
│   └── mqtt/              # MQTT客户端
├── frontend/              # 前端项目
│   ├── src/
│   │   ├── api/           # API接口
│   │   ├── components/    # 组件
│   │   ├── layout/        # 布局
│   │   ├── router/        # 路由
│   │   ├── stores/        # 状态管理
│   │   ├── utils/         # 工具函数
│   │   └── views/         # 页面
│   └── package.json
├── miniprogram/           # 小程序项目
├── configs/               # 配置文件
├── scripts/               # 脚本文件
├── docs/                  # 文档
├── docker-compose.yml     # Docker编排
├── Dockerfile            # Docker镜像
├── Makefile              # 构建脚本
└── README.md
```

## 🔧 配置说明

### 数据库配置
```yaml
database:
  host: localhost
  port: 3307
  username: root
  password: "123456"
  database: electronic_price_tag
  charset: utf8mb4
```

### Redis配置
```yaml
redis:
  host: localhost
  port: 6379
  password: ""
  db: 0
```

### MQTT配置
```yaml
mqtt:
  broker: "tcp://localhost:1883"
  username: ""
  password: ""
  client_id: "electronic-price-tag-server"
```