package middleware

import (
	"electronic-price-tag/pkg/logger"
	"net/http"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
)

// CustomClaims JWT自定义声明
type CustomClaims struct {
	UserID     uint   `json:"user_id"`
	Username   string `json:"username"`
	MerchantID string `json:"merchant_id"`
	Roles      []string `json:"roles"`
	jwt.StandardClaims
}

// JWTAuth JWT认证中间件
func JWTAuth(secretKey string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取token
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "请求头中auth为空",
			})
			c.Abort()
			return
		}

		// 检查token格式
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "请求头中auth格式有误",
			})
			c.Abort()
			return
		}

		// 解析token
		claims, err := ParseToken(parts[1], secretKey)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "无效的token",
			})
			c.Abort()
			return
		}

		// 检查token是否过期
		if claims.ExpiresAt < time.Now().Unix() {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "token已过期",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		c.Set("claims", claims)
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("merchant_id", claims.MerchantID)
		c.Set("roles", claims.Roles)

		c.Next()
	}
}

// GenerateToken 生成JWT token
func GenerateToken(userID uint, username, merchantID string, roles []string, secretKey string, expireTime int) (string, error) {
	claims := CustomClaims{
		UserID:     userID,
		Username:   username,
		MerchantID: merchantID,
		Roles:      roles,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: time.Now().Add(time.Duration(expireTime) * time.Second).Unix(),
			IssuedAt:  time.Now().Unix(),
			Issuer:    "electronic-price-tag",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(secretKey))
}

// ParseToken 解析JWT token
func ParseToken(tokenString, secretKey string) (*CustomClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(secretKey), nil
	})

	if err != nil {
		logger.Error("Failed to parse token: %v", err)
		return nil, err
	}

	if claims, ok := token.Claims.(*CustomClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, jwt.NewValidationError("invalid token", jwt.ValidationErrorClaimsInvalid)
}

// RefreshToken 刷新token
func RefreshToken(tokenString, secretKey string, expireTime int) (string, error) {
	claims, err := ParseToken(tokenString, secretKey)
	if err != nil {
		return "", err
	}

	// 检查token是否在刷新期内（过期前30分钟内可以刷新）
	if time.Unix(claims.ExpiresAt, 0).Sub(time.Now()) > 30*time.Minute {
		return "", jwt.NewValidationError("token not in refresh period", jwt.ValidationErrorClaimsInvalid)
	}

	// 生成新token
	return GenerateToken(claims.UserID, claims.Username, claims.MerchantID, claims.Roles, secretKey, expireTime)
}

// OptionalAuth 可选认证中间件（不强制要求认证）
func OptionalAuth(secretKey string) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader != "" {
			parts := strings.SplitN(authHeader, " ", 2)
			if len(parts) == 2 && parts[0] == "Bearer" {
				claims, err := ParseToken(parts[1], secretKey)
				if err == nil && claims.ExpiresAt >= time.Now().Unix() {
					c.Set("claims", claims)
					c.Set("user_id", claims.UserID)
					c.Set("username", claims.Username)
					c.Set("merchant_id", claims.MerchantID)
					c.Set("roles", claims.Roles)
				}
			}
		}
		c.Next()
	}
}
