package middleware

import (
	"electronic-price-tag/pkg/logger"
	"net/http"
	"strings"

	"github.com/casbin/casbin/v2"
	gormadapter "github.com/casbin/gorm-adapter/v3"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// InitCasbin 初始化Casbin权限系统
func InitCasbin(db *gorm.DB) (*casbin.Enforcer, error) {
	// 创建Gorm适配器
	adapter, err := gormadapter.NewAdapterByDB(db)
	if err != nil {
		return nil, err
	}

	// 创建权限模型
	model := `
[request_definition]
r = sub, dom, obj, act

[policy_definition]
p = sub, dom, obj, act

[role_definition]
g = _, _, _

[policy_effect]
e = some(where (p.eft == allow))

[matchers]
m = g(r.sub, p.sub, r.dom) && r.dom == p.dom && keyMatch2(r.obj, p.obj) && r.act == p.act
`

	// 创建执行器
	enforcer, err := casbin.NewEnforcer()
	if err != nil {
		return nil, err
	}

	// 设置模型
	if err := enforcer.SetModel(casbin.NewModelFromString(model)); err != nil {
		return nil, err
	}

	// 设置适配器
	enforcer.SetAdapter(adapter)

	// 加载策略
	if err := enforcer.LoadPolicy(); err != nil {
		return nil, err
	}

	// 启用自动保存
	enforcer.EnableAutoSave(true)

	// 初始化基础权限策略
	if err := initBasePolicies(enforcer); err != nil {
		return nil, err
	}

	return enforcer, nil
}

// AuthInterceptor 权限拦截器中间件
func AuthInterceptor(enforcer *casbin.Enforcer) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户信息
		claims, exists := c.Get("claims")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "未认证用户",
			})
			c.Abort()
			return
		}

		customClaims := claims.(*CustomClaims)

		// 构造访问三元组
		obj := c.Request.URL.Path
		act := c.Request.Method
		domain := customClaims.MerchantID

		// 超级管理员跳过权限检查
		if customClaims.Username == "admin" {
			c.Next()
			return
		}

		// 检查用户权限
		allowed := false
		for _, role := range customClaims.Roles {
			if ok, err := enforcer.Enforce(role, domain, obj, act); err != nil {
				logger.Error("Casbin enforce error: %v", err)
				c.JSON(http.StatusInternalServerError, gin.H{
					"code": 500,
					"msg":  "权限检查失败",
				})
				c.Abort()
				return
			} else if ok {
				allowed = true
				break
			}
		}

		if !allowed {
			logger.Warn("Access denied for user %s to %s %s", customClaims.Username, act, obj)
			c.JSON(http.StatusForbidden, gin.H{
				"code": 403,
				"msg":  "权限不足",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// DataScopeFilter 数据权限过滤器
func DataScopeFilter(db *gorm.DB, userID uint, merchantID string) func(*gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		// 如果是超级管理员，不限制数据范围
		if isAdmin(userID) {
			return tx
		}

		// 根据商户ID限制数据范围
		return tx.Where("merchant_id = ?", merchantID)
	}
}

// isAdmin 检查是否为管理员
func isAdmin(userID uint) bool {
	// 这里可以根据实际需求实现管理员判断逻辑
	// 简单实现：用户ID为1的是管理员
	return userID == 1
}

// initBasePolicies 初始化基础权限策略
func initBasePolicies(enforcer *casbin.Enforcer) error {
	// 为admin角色添加所有权限
	policies := [][]string{
		{"admin", "*", "/api/*", "GET"},
		{"admin", "*", "/api/*", "POST"},
		{"admin", "*", "/api/*", "PUT"},
		{"admin", "*", "/api/*", "DELETE"},
		{"admin", "*", "/api/*", "PATCH"},
	}

	for _, policy := range policies {
		if _, err := enforcer.AddPolicy(policy); err != nil {
			logger.Error("Failed to add policy %v: %v", policy, err)
		}
	}

	return nil
}

// AddUserRole 添加用户角色
func AddUserRole(enforcer *casbin.Enforcer, username, role, domain string) error {
	_, err := enforcer.AddGroupingPolicy(username, role, domain)
	return err
}

// RemoveUserRole 移除用户角色
func RemoveUserRole(enforcer *casbin.Enforcer, username, role, domain string) error {
	_, err := enforcer.RemoveGroupingPolicy(username, role, domain)
	return err
}

// AddRolePermission 添加角色权限
func AddRolePermission(enforcer *casbin.Enforcer, role, domain, object, action string) error {
	_, err := enforcer.AddPolicy(role, domain, object, action)
	return err
}

// RemoveRolePermission 移除角色权限
func RemoveRolePermission(enforcer *casbin.Enforcer, role, domain, object, action string) error {
	_, err := enforcer.RemovePolicy(role, domain, object, action)
	return err
}

// GetUserRoles 获取用户角色
func GetUserRoles(enforcer *casbin.Enforcer, username, domain string) []string {
	roles := enforcer.GetRolesForUserInDomain(username, domain)
	return roles
}

// GetRolePermissions 获取角色权限
func GetRolePermissions(enforcer *casbin.Enforcer, role, domain string) [][]string {
	permissions := enforcer.GetPermissionsForUserInDomain(role, domain)
	return permissions
}

// CheckPermission 检查权限
func CheckPermission(enforcer *casbin.Enforcer, subject, domain, object, action string) bool {
	ok, err := enforcer.Enforce(subject, domain, object, action)
	if err != nil {
		logger.Error("Check permission error: %v", err)
		return false
	}
	return ok
}

// keyMatch2 自定义匹配函数
func keyMatch2(key1, key2 string) bool {
	key2 = strings.Replace(key2, "/*", "/.*", -1)
	return regexMatch(key1, "^"+key2+"$")
}

// regexMatch 正则匹配
func regexMatch(key1, key2 string) bool {
	// 这里应该使用正则表达式匹配，简化实现
	if key2 == "^/api/.*$" {
		return strings.HasPrefix(key1, "/api/")
	}
	return key1 == key2
}
