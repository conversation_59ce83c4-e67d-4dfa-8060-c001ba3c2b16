package service

import (
	"context"
	"electronic-price-tag/internal/middleware"
	"electronic-price-tag/internal/model"
	"electronic-price-tag/pkg/logger"
	"errors"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type AuthService interface {
	Login(username, password string) (*LoginResponse, error)
	Logout(token string) error
	RefreshToken(token string) (*LoginResponse, error)
	ChangePassword(userID uint, oldPassword, newPassword string) error
	ResetPassword(userID uint, newPassword string) error
}

type authService struct {
	db  *gorm.DB
	rdb *redis.Client
}

type LoginResponse struct {
	Token     string      `json:"token"`
	ExpiresAt int64       `json:"expires_at"`
	User      *model.User `json:"user"`
}

func NewAuthService(db *gorm.DB, rdb *redis.Client) AuthService {
	return &authService{
		db:  db,
		rdb: rdb,
	}
}

// Login 用户登录
func (s *authService) Login(username, password string) (*LoginResponse, error) {
	// 查找用户
	var user model.User
	if err := s.db.Preload("Roles").Preload("Merchant").Where("username = ? AND status = 1", username).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("用户名或密码错误")
		}
		logger.Error("Failed to find user: %v", err)
		return nil, errors.New("登录失败")
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return nil, errors.New("用户名或密码错误")
	}

	// 检查商户状态
	if user.Merchant != nil && user.Merchant.Status != 1 {
		return nil, errors.New("商户已被禁用")
	}

	// 获取用户角色
	var roles []string
	for _, role := range user.Roles {
		if role.Status == 1 {
			roles = append(roles, role.Code)
		}
	}

	// 生成JWT token
	token, err := middleware.GenerateToken(
		user.ID,
		user.Username,
		user.MerchantID,
		roles,
		"electronic-price-tag-secret-key", // 这里应该从配置读取
		24*60*60, // 24小时
	)
	if err != nil {
		logger.Error("Failed to generate token: %v", err)
		return nil, errors.New("生成token失败")
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLoginAt = &now
	s.db.Model(&user).Update("last_login_at", now)

	// 将token存储到Redis（用于登出时失效）
	ctx := context.Background()
	tokenKey := fmt.Sprintf("token:%d", user.ID)
	s.rdb.Set(ctx, tokenKey, token, 24*time.Hour)

	// 清除密码字段
	user.Password = ""

	return &LoginResponse{
		Token:     token,
		ExpiresAt: time.Now().Add(24 * time.Hour).Unix(),
		User:      &user,
	}, nil
}

// Logout 用户登出
func (s *authService) Logout(token string) error {
	// 解析token获取用户ID
	claims, err := middleware.ParseToken(token, "electronic-price-tag-secret-key")
	if err != nil {
		return err
	}

	// 从Redis删除token
	ctx := context.Background()
	tokenKey := fmt.Sprintf("token:%d", claims.UserID)
	s.rdb.Del(ctx, tokenKey)

	// 将token加入黑名单
	blacklistKey := fmt.Sprintf("blacklist:%s", token)
	s.rdb.Set(ctx, blacklistKey, "1", time.Until(time.Unix(claims.ExpiresAt, 0)))

	return nil
}

// RefreshToken 刷新token
func (s *authService) RefreshToken(token string) (*LoginResponse, error) {
	// 解析旧token
	claims, err := middleware.ParseToken(token, "electronic-price-tag-secret-key")
	if err != nil {
		return nil, err
	}

	// 检查token是否在黑名单中
	ctx := context.Background()
	blacklistKey := fmt.Sprintf("blacklist:%s", token)
	if exists := s.rdb.Exists(ctx, blacklistKey).Val(); exists > 0 {
		return nil, errors.New("token已失效")
	}

	// 查找用户
	var user model.User
	if err := s.db.Preload("Roles").Preload("Merchant").Where("id = ? AND status = 1", claims.UserID).First(&user).Error; err != nil {
		return nil, errors.New("用户不存在或已被禁用")
	}

	// 获取用户角色
	var roles []string
	for _, role := range user.Roles {
		if role.Status == 1 {
			roles = append(roles, role.Code)
		}
	}

	// 生成新token
	newToken, err := middleware.GenerateToken(
		user.ID,
		user.Username,
		user.MerchantID,
		roles,
		"electronic-price-tag-secret-key",
		24*60*60,
	)
	if err != nil {
		return nil, err
	}

	// 将旧token加入黑名单
	blacklistKey = fmt.Sprintf("blacklist:%s", token)
	s.rdb.Set(ctx, blacklistKey, "1", time.Until(time.Unix(claims.ExpiresAt, 0)))

	// 存储新token
	tokenKey := fmt.Sprintf("token:%d", user.ID)
	s.rdb.Set(ctx, tokenKey, newToken, 24*time.Hour)

	// 清除密码字段
	user.Password = ""

	return &LoginResponse{
		Token:     newToken,
		ExpiresAt: time.Now().Add(24 * time.Hour).Unix(),
		User:      &user,
	}, nil
}

// ChangePassword 修改密码
func (s *authService) ChangePassword(userID uint, oldPassword, newPassword string) error {
	// 查找用户
	var user model.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return errors.New("用户不存在")
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(oldPassword)); err != nil {
		return errors.New("原密码错误")
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return errors.New("密码加密失败")
	}

	// 更新密码
	if err := s.db.Model(&user).Update("password", string(hashedPassword)).Error; err != nil {
		return errors.New("密码更新失败")
	}

	return nil
}

// ResetPassword 重置密码
func (s *authService) ResetPassword(userID uint, newPassword string) error {
	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return errors.New("密码加密失败")
	}

	// 更新密码
	if err := s.db.Model(&model.User{}).Where("id = ?", userID).Update("password", string(hashedPassword)).Error; err != nil {
		return errors.New("密码重置失败")
	}

	return nil
}

// HashPassword 密码加密
func HashPassword(password string) (string, error) {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedPassword), nil
}

// CheckPassword 验证密码
func CheckPassword(hashedPassword, password string) error {
	return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
}
