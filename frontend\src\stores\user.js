import { defineStore } from 'pinia'
import { login, logout, getUserInfo } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    userInfo: null,
    permissions: []
  }),

  getters: {
    isLoggedIn: (state) => !!state.token,
    username: (state) => state.userInfo?.username || '',
    realName: (state) => state.userInfo?.real_name || '',
    avatar: (state) => state.userInfo?.avatar || '',
    roles: (state) => state.userInfo?.roles || [],
    merchantId: (state) => state.userInfo?.merchant_id || ''
  },

  actions: {
    // 登录
    async login(loginForm) {
      try {
        const response = await login(loginForm)
        const { token, user } = response.data
        
        this.token = token
        this.userInfo = user
        setToken(token)
        
        return response
      } catch (error) {
        throw error
      }
    },

    // 登出
    async logout() {
      try {
        if (this.token) {
          await logout()
        }
      } catch (error) {
        console.error('Logout error:', error)
      } finally {
        this.token = ''
        this.userInfo = null
        this.permissions = []
        removeToken()
      }
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        const response = await getUserInfo()
        this.userInfo = response.data
        return response.data
      } catch (error) {
        throw error
      }
    },

    // 初始化用户信息
    async initUserInfo() {
      if (this.token && !this.userInfo) {
        try {
          await this.getUserInfo()
        } catch (error) {
          console.error('Init user info error:', error)
          this.logout()
        }
      }
    },

    // 检查权限
    hasPermission(permission) {
      if (!permission) return true
      if (this.roles.some(role => role.code === 'admin')) return true
      return this.permissions.includes(permission)
    },

    // 检查角色
    hasRole(role) {
      if (!role) return true
      return this.roles.some(r => r.code === role)
    }
  }
})
