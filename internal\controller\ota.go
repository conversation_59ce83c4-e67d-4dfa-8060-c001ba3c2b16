package controller

import (
	"electronic-price-tag/internal/service"
	"io"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type OTAController struct {
	otaService service.OTAService
}

type UploadFirmwareFormRequest struct {
	Version     string `form:"version" binding:"required"`
	Model       string `form:"model" binding:"required"`
	Description string `form:"description"`
	ChangeLog   string `form:"change_log"`
	IsForced    bool   `form:"is_forced"`
}

func NewOTAController(otaService service.OTAService) *OTAController {
	return &OTAController{
		otaService: otaService,
	}
}

// GetFirmwares 获取固件列表
// @Summary 获取固件列表
// @Description 分页获取固件列表
// @Tags OTA管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param model query string false "设备型号"
// @Success 200 {object} service.FirmwareListResponse
// @Failure 400 {object} map[string]interface{}
// @Router /api/ota/firmwares [get]
func (c *OTAController) GetFirmwares(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	model := ctx.Query("model")

	response, err := c.otaService.GetFirmwares(page, pageSize, model)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": response,
	})
}

// GetFirmwareByID 根据ID获取固件
// @Summary 根据ID获取固件
// @Description 根据固件ID获取固件详细信息
// @Tags OTA管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "固件ID"
// @Success 200 {object} model.Firmware
// @Failure 400 {object} map[string]interface{}
// @Router /api/ota/firmwares/{id} [get]
func (c *OTAController) GetFirmwareByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的固件ID",
		})
		return
	}

	firmware, err := c.otaService.GetFirmwareByID(uint(id))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code": 404,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": firmware,
	})
}

// UploadFirmware 上传固件
// @Summary 上传固件
// @Description 上传新的固件文件
// @Tags OTA管理
// @Accept multipart/form-data
// @Produce json
// @Security ApiKeyAuth
// @Param version formData string true "版本号"
// @Param model formData string true "设备型号"
// @Param description formData string false "描述"
// @Param change_log formData string false "更新日志"
// @Param is_forced formData bool false "是否强制更新"
// @Param file formData file true "固件文件"
// @Success 200 {object} model.Firmware
// @Failure 400 {object} map[string]interface{}
// @Router /api/ota/firmwares [post]
func (c *OTAController) UploadFirmware(ctx *gin.Context) {
	var req UploadFirmwareFormRequest
	if err := ctx.ShouldBind(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	// 获取上传的文件
	file, header, err := ctx.Request.FormFile("file")
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "文件上传失败",
			"data": err.Error(),
		})
		return
	}
	defer file.Close()

	// 读取文件内容
	fileData, err := io.ReadAll(file)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "读取文件失败",
			"data": err.Error(),
		})
		return
	}

	// 构造上传请求
	uploadReq := &service.UploadFirmwareRequest{
		Version:     req.Version,
		Model:       req.Model,
		FileName:    header.Filename,
		FileData:    fileData,
		Description: req.Description,
		ChangeLog:   req.ChangeLog,
		IsForced:    req.IsForced,
	}

	firmware, err := c.otaService.UploadFirmware(uploadReq)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "上传成功",
		"data": firmware,
	})
}

// DeleteFirmware 删除固件
// @Summary 删除固件
// @Description 删除固件文件
// @Tags OTA管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "固件ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/ota/firmwares/{id} [delete]
func (c *OTAController) DeleteFirmware(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的固件ID",
		})
		return
	}

	if err := c.otaService.DeleteFirmware(uint(id)); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "删除成功",
	})
}

// GetOTATasks 获取OTA任务列表
// @Summary 获取OTA任务列表
// @Description 分页获取OTA任务列表
// @Tags OTA管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} service.OTATaskListResponse
// @Failure 400 {object} map[string]interface{}
// @Router /api/ota/tasks [get]
func (c *OTAController) GetOTATasks(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	// 获取当前用户的商户ID
	merchantID, _ := ctx.Get("merchant_id")

	response, err := c.otaService.GetOTATasks(merchantID.(string), page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": response,
	})
}

// GetOTATaskByID 根据ID获取OTA任务
// @Summary 根据ID获取OTA任务
// @Description 根据任务ID获取OTA任务详细信息
// @Tags OTA管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "任务ID"
// @Success 200 {object} model.OTATask
// @Failure 400 {object} map[string]interface{}
// @Router /api/ota/tasks/{id} [get]
func (c *OTAController) GetOTATaskByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的任务ID",
		})
		return
	}

	task, err := c.otaService.GetOTATaskByID(uint(id))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code": 404,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": task,
	})
}

// CreateOTATask 创建OTA任务
// @Summary 创建OTA任务
// @Description 创建新的OTA升级任务
// @Tags OTA管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body service.CreateOTATaskRequest true "创建OTA任务请求"
// @Success 200 {object} model.OTATask
// @Failure 400 {object} map[string]interface{}
// @Router /api/ota/tasks [post]
func (c *OTAController) CreateOTATask(ctx *gin.Context) {
	var req service.CreateOTATaskRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	// 设置当前用户信息
	merchantID, _ := ctx.Get("merchant_id")
	userID, _ := ctx.Get("user_id")
	req.MerchantID = merchantID.(string)
	req.CreatedBy = userID.(uint)

	task, err := c.otaService.CreateOTATask(&req)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "创建成功",
		"data": task,
	})
}

// StartOTATask 启动OTA任务
// @Summary 启动OTA任务
// @Description 启动OTA升级任务
// @Tags OTA管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "任务ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/ota/tasks/{id}/start [post]
func (c *OTAController) StartOTATask(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的任务ID",
		})
		return
	}

	if err := c.otaService.StartOTATask(uint(id)); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "任务启动成功",
	})
}

// CancelOTATask 取消OTA任务
// @Summary 取消OTA任务
// @Description 取消正在执行的OTA任务
// @Tags OTA管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "任务ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/ota/tasks/{id}/cancel [post]
func (c *OTAController) CancelOTATask(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的任务ID",
		})
		return
	}

	if err := c.otaService.CancelOTATask(uint(id)); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "任务取消成功",
	})
}

// GetOTARecords 获取OTA升级记录
// @Summary 获取OTA升级记录
// @Description 获取OTA任务的升级记录列表
// @Tags OTA管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "任务ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} service.OTARecordListResponse
// @Failure 400 {object} map[string]interface{}
// @Router /api/ota/tasks/{id}/records [get]
func (c *OTAController) GetOTARecords(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的任务ID",
		})
		return
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	response, err := c.otaService.GetOTARecords(uint(id), page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": response,
	})
}

// GetUpdatePackage 获取升级包（设备端调用）
func (c *OTAController) GetUpdatePackage(ctx *gin.Context) {
	deviceModel := ctx.Query("model")
	currentVer := ctx.Query("version")

	if deviceModel == "" || currentVer == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "缺少必要参数",
		})
		return
	}

	packageData, err := c.otaService.GetUpdatePackage(deviceModel, currentVer)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	// 直接返回二进制数据
	ctx.Header("Content-Type", "application/octet-stream")
	ctx.Header("Content-Length", strconv.Itoa(len(packageData)))
	ctx.Data(http.StatusOK, "application/octet-stream", packageData)
}

// HandleOTAProgress 处理OTA进度（设备端调用）
func (c *OTAController) HandleOTAProgress(ctx *gin.Context) {
	deviceSN := ctx.Query("sn")
	if deviceSN == "" {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "缺少设备SN",
		})
		return
	}

	var progress service.OTAProgress
	if err := ctx.ShouldBindJSON(&progress); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	if err := c.otaService.HandleOTAProgress(deviceSN, &progress); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "进度更新成功",
	})
}
