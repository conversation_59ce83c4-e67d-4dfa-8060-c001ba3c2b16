package model

import (
	"time"
	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	Username    string         `json:"username" gorm:"uniqueIndex;size:50;not null"`
	Password    string         `json:"-" gorm:"size:255;not null"`
	Email       string         `json:"email" gorm:"uniqueIndex;size:100"`
	Phone       string         `json:"phone" gorm:"size:20"`
	RealName    string         `json:"real_name" gorm:"size:50"`
	Avatar      string         `json:"avatar" gorm:"size:255"`
	Status      int            `json:"status" gorm:"default:1;comment:1-正常,0-禁用"`
	MerchantID  string         `json:"merchant_id" gorm:"size:32;index"`
	LastLoginAt *time.Time     `json:"last_login_at"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Roles       []Role         `json:"roles" gorm:"many2many:user_roles;"`
	Merchant    *Merchant      `json:"merchant,omitempty" gorm:"foreignKey:MerchantID;references:ID"`
}

// Role 角色模型
type Role struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	Name        string         `json:"name" gorm:"uniqueIndex;size:50;not null"`
	Code        string         `json:"code" gorm:"uniqueIndex;size:50;not null"`
	Description string         `json:"description" gorm:"size:255"`
	MerchantID  string         `json:"merchant_id" gorm:"size:32;index"`
	Status      int            `json:"status" gorm:"default:1;comment:1-正常,0-禁用"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Users       []User         `json:"users,omitempty" gorm:"many2many:user_roles;"`
	Permissions []Permission   `json:"permissions,omitempty" gorm:"many2many:role_permissions;"`
}

// Permission 权限模型
type Permission struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	Name        string         `json:"name" gorm:"size:100;not null"`
	Code        string         `json:"code" gorm:"uniqueIndex;size:100;not null"`
	Type        string         `json:"type" gorm:"size:20;not null;comment:menu,button,api"`
	Path        string         `json:"path" gorm:"size:255"`
	Method      string         `json:"method" gorm:"size:10"`
	ParentID    uint           `json:"parent_id" gorm:"default:0"`
	Sort        int            `json:"sort" gorm:"default:0"`
	Icon        string         `json:"icon" gorm:"size:50"`
	Component   string         `json:"component" gorm:"size:255"`
	Status      int            `json:"status" gorm:"default:1;comment:1-正常,0-禁用"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Roles       []Role         `json:"roles,omitempty" gorm:"many2many:role_permissions;"`
	Children    []Permission   `json:"children,omitempty" gorm:"foreignKey:ParentID"`
}

// Merchant 商户模型
type Merchant struct {
	ID          string         `json:"id" gorm:"primarykey;size:32"`
	Name        string         `json:"name" gorm:"size:100;not null"`
	Code        string         `json:"code" gorm:"uniqueIndex;size:50;not null"`
	Contact     string         `json:"contact" gorm:"size:50"`
	Phone       string         `json:"phone" gorm:"size:20"`
	Email       string         `json:"email" gorm:"size:100"`
	Address     string         `json:"address" gorm:"size:255"`
	Logo        string         `json:"logo" gorm:"size:255"`
	Status      int            `json:"status" gorm:"default:1;comment:1-正常,0-禁用"`
	ExpireAt    *time.Time     `json:"expire_at"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Users       []User         `json:"users,omitempty" gorm:"foreignKey:MerchantID;references:ID"`
	Devices     []Device       `json:"devices,omitempty" gorm:"foreignKey:MerchantID;references:ID"`
}

// UserRole 用户角色关联表
type UserRole struct {
	UserID uint `json:"user_id" gorm:"primarykey"`
	RoleID uint `json:"role_id" gorm:"primarykey"`
}

// RolePermission 角色权限关联表
type RolePermission struct {
	RoleID       uint `json:"role_id" gorm:"primarykey"`
	PermissionID uint `json:"permission_id" gorm:"primarykey"`
}

// TableName 设置表名
func (User) TableName() string {
	return "users"
}

func (Role) TableName() string {
	return "roles"
}

func (Permission) TableName() string {
	return "permissions"
}

func (Merchant) TableName() string {
	return "merchants"
}

func (UserRole) TableName() string {
	return "user_roles"
}

func (RolePermission) TableName() string {
	return "role_permissions"
}
