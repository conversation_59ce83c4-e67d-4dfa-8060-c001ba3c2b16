package router

import (
	"electronic-price-tag/internal/controller"
	"electronic-price-tag/internal/middleware"
	"electronic-price-tag/internal/service"

	"github.com/casbin/casbin/v2"
	"github.com/gin-gonic/gin"
)

// SetupRouter 设置路由
func SetupRouter(services *service.Services, enforcer *casbin.Enforcer) *gin.Engine {
	r := gin.New()

	// 添加中间件
	r.Use(gin.Recovery())
	r.Use(middleware.Logger())
	r.Use(middleware.CORS())
	r.Use(middleware.ErrorLogger())

	// 创建控制器
	authController := controller.NewAuthController(services.Auth)
	userController := controller.NewUserController(services.User)
	deviceController := controller.NewDeviceController(services.Device)
	templateController := controller.NewTemplateController(services.Template)
	productController := controller.NewProductController(services.Product)
	otaController := controller.NewOTAController(services.OTA)

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSO<PERSON>(200, gin.H{"status": "ok"})
	})

	// API路由组
	api := r.Group("/api")
	{
		// 认证相关路由（无需认证）
		auth := api.Group("/auth")
		{
			auth.POST("/login", authController.Login)
			auth.POST("/logout", middleware.JWTAuth("electronic-price-tag-secret-key"), authController.Logout)
			auth.POST("/refresh", authController.RefreshToken)
		}

		// 需要认证的路由
		authenticated := api.Group("")
		authenticated.Use(middleware.JWTAuth("electronic-price-tag-secret-key"))
		authenticated.Use(middleware.AuthInterceptor(enforcer))
		{
			// 用户管理
			users := authenticated.Group("/users")
			{
				users.GET("", userController.GetUsers)
				users.GET("/:id", userController.GetUserByID)
				users.POST("", userController.CreateUser)
				users.PUT("/:id", userController.UpdateUser)
				users.DELETE("/:id", userController.DeleteUser)
				users.GET("/:id/roles", userController.GetUserRoles)
				users.POST("/:id/roles", userController.AssignRoles)
				users.GET("/:id/permissions", userController.GetUserPermissions)
				users.POST("/:id/change-password", userController.ChangePassword)
			}

			// 设备管理
			devices := authenticated.Group("/devices")
			{
				devices.GET("", deviceController.GetDevices)
				devices.GET("/:id", deviceController.GetDeviceByID)
				devices.POST("", deviceController.CreateDevice)
				devices.PUT("/:id", deviceController.UpdateDevice)
				devices.DELETE("/:id", deviceController.DeleteDevice)
				devices.POST("/:id/commands", deviceController.SendCommand)
				devices.GET("/:id/commands", deviceController.GetDeviceCommands)
				devices.GET("/:id/logs", deviceController.GetDeviceLogs)
				devices.POST("/batch-update", deviceController.BatchUpdateDisplay)
				devices.POST("/activate", deviceController.ActivateDevice)
			}

			// 模板管理
			templates := authenticated.Group("/templates")
			{
				templates.GET("", templateController.GetTemplates)
				templates.GET("/:id", templateController.GetTemplateByID)
				templates.POST("", templateController.CreateTemplate)
				templates.PUT("/:id", templateController.UpdateTemplate)
				templates.DELETE("/:id", templateController.DeleteTemplate)
				templates.GET("/:id/elements", templateController.GetTemplateElements)
				templates.POST("/:id/elements", templateController.CreateTemplateElement)
				templates.PUT("/elements/:id", templateController.UpdateTemplateElement)
				templates.DELETE("/elements/:id", templateController.DeleteTemplateElement)
				templates.POST("/:id/render", templateController.RenderTemplate)
				templates.POST("/:id/generate-epd", templateController.GenerateEPD)
			}

			// 商品管理
			products := authenticated.Group("/products")
			{
				products.GET("", productController.GetProducts)
				products.GET("/:id", productController.GetProductByID)
				products.POST("", productController.CreateProduct)
				products.PUT("/:id", productController.UpdateProduct)
				products.DELETE("/:id", productController.DeleteProduct)
				products.POST("/:id/price", productController.UpdateProductPrice)
				products.GET("/:id/price-logs", productController.GetProductPriceLogs)
			}

			// 商品分类
			categories := authenticated.Group("/categories")
			{
				categories.GET("", productController.GetCategories)
				categories.POST("", productController.CreateCategory)
				categories.PUT("/:id", productController.UpdateCategory)
				categories.DELETE("/:id", productController.DeleteCategory)
			}

			// OTA管理
			ota := authenticated.Group("/ota")
			{
				// 固件管理
				firmwares := ota.Group("/firmwares")
				{
					firmwares.GET("", otaController.GetFirmwares)
					firmwares.GET("/:id", otaController.GetFirmwareByID)
					firmwares.POST("", otaController.UploadFirmware)
					firmwares.DELETE("/:id", otaController.DeleteFirmware)
				}

				// 任务管理
				tasks := ota.Group("/tasks")
				{
					tasks.GET("", otaController.GetOTATasks)
					tasks.GET("/:id", otaController.GetOTATaskByID)
					tasks.POST("", otaController.CreateOTATask)
					tasks.POST("/:id/start", otaController.StartOTATask)
					tasks.POST("/:id/cancel", otaController.CancelOTATask)
					tasks.GET("/:id/records", otaController.GetOTARecords)
				}
			}
		}

		// 设备API（设备端调用，使用设备认证）
		deviceAPI := api.Group("/device")
		{
			deviceAPI.POST("/register", deviceController.RegisterDevice)
			deviceAPI.POST("/heartbeat", deviceController.DeviceHeartbeat)
			deviceAPI.GET("/update-package", otaController.GetUpdatePackage)
			deviceAPI.POST("/ota-progress", otaController.HandleOTAProgress)
		}
	}

	// WebSocket路由
	ws := r.Group("/ws")
	{
		ws.GET("/device/:sn", deviceController.DeviceWebSocket)
		ws.GET("/monitor", deviceController.MonitorWebSocket)
	}

	// 静态文件服务
	r.Static("/static", "./static")
	r.Static("/uploads", "./uploads")

	return r
}
