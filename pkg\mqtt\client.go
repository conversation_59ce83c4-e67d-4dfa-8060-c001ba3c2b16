package mqtt

import (
	"crypto/tls"
	"electronic-price-tag/internal/config"
	"electronic-price-tag/pkg/logger"
	"fmt"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

type Client struct {
	client mqtt.Client
	config config.MQTTConfig
}

// NewClient 创建MQTT客户端
func NewClient(cfg config.MQTTConfig) (*Client, error) {
	opts := mqtt.NewClientOptions()
	opts.AddBroker(cfg.Broker)
	opts.SetClientID(cfg.ClientID)
	opts.SetUsername(cfg.Username)
	opts.SetPassword(cfg.Password)
	opts.SetCleanSession(true)
	opts.SetAutoReconnect(true)
	opts.SetConnectTimeout(30 * time.Second)
	opts.SetKeepAlive(60 * time.Second)
	opts.SetPingTimeout(10 * time.Second)
	opts.SetWriteTimeout(10 * time.Second)
	
	// 设置TLS配置
	opts.SetTLSConfig(&tls.Config{InsecureSkipVerify: true})
	
	// 设置连接回调
	opts.SetOnConnectHandler(func(client mqtt.Client) {
		logger.Info("MQTT connected to broker: %s", cfg.Broker)
	})
	
	// 设置连接丢失回调
	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		logger.Error("MQTT connection lost: %v", err)
	})
	
	// 设置重连回调
	opts.SetReconnectingHandler(func(client mqtt.Client, opts *mqtt.ClientOptions) {
		logger.Info("MQTT reconnecting to broker...")
	})

	client := mqtt.NewClient(opts)
	
	// 连接到MQTT代理
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		return nil, fmt.Errorf("failed to connect to MQTT broker: %w", token.Error())
	}

	mqttClient := &Client{
		client: client,
		config: cfg,
	}

	// 订阅设备主题
	if err := mqttClient.subscribeDeviceTopics(); err != nil {
		return nil, fmt.Errorf("failed to subscribe device topics: %w", err)
	}

	return mqttClient, nil
}

// Publish 发布消息
func (c *Client) Publish(topic string, qos byte, retained bool, payload interface{}) error {
	token := c.client.Publish(topic, qos, retained, payload)
	token.Wait()
	if token.Error() != nil {
		logger.Error("Failed to publish message to topic %s: %v", topic, token.Error())
		return token.Error()
	}
	logger.Debug("Published message to topic: %s", topic)
	return nil
}

// Subscribe 订阅主题
func (c *Client) Subscribe(topic string, qos byte, callback mqtt.MessageHandler) error {
	token := c.client.Subscribe(topic, qos, callback)
	token.Wait()
	if token.Error() != nil {
		logger.Error("Failed to subscribe to topic %s: %v", topic, token.Error())
		return token.Error()
	}
	logger.Info("Subscribed to topic: %s", topic)
	return nil
}

// Unsubscribe 取消订阅
func (c *Client) Unsubscribe(topics ...string) error {
	token := c.client.Unsubscribe(topics...)
	token.Wait()
	if token.Error() != nil {
		logger.Error("Failed to unsubscribe from topics: %v", token.Error())
		return token.Error()
	}
	logger.Info("Unsubscribed from topics: %v", topics)
	return nil
}

// Disconnect 断开连接
func (c *Client) Disconnect(quiesce uint) {
	c.client.Disconnect(quiesce)
	logger.Info("MQTT client disconnected")
}

// IsConnected 检查连接状态
func (c *Client) IsConnected() bool {
	return c.client.IsConnected()
}

// subscribeDeviceTopics 订阅设备相关主题
func (c *Client) subscribeDeviceTopics() error {
	topics := map[string]mqtt.MessageHandler{
		"device/+/status":    c.handleDeviceStatus,
		"device/+/heartbeat": c.handleDeviceHeartbeat,
		"device/+/response":  c.handleDeviceResponse,
		"device/+/log":       c.handleDeviceLog,
		"device/+/ota":       c.handleDeviceOTA,
	}

	for topic, handler := range topics {
		if err := c.Subscribe(topic, 1, handler); err != nil {
			return err
		}
	}

	return nil
}

// handleDeviceStatus 处理设备状态消息
func (c *Client) handleDeviceStatus(client mqtt.Client, msg mqtt.Message) {
	logger.Debug("Received device status: topic=%s, payload=%s", msg.Topic(), string(msg.Payload()))
	// TODO: 处理设备状态更新
}

// handleDeviceHeartbeat 处理设备心跳消息
func (c *Client) handleDeviceHeartbeat(client mqtt.Client, msg mqtt.Message) {
	logger.Debug("Received device heartbeat: topic=%s, payload=%s", msg.Topic(), string(msg.Payload()))
	// TODO: 更新设备最后在线时间
}

// handleDeviceResponse 处理设备响应消息
func (c *Client) handleDeviceResponse(client mqtt.Client, msg mqtt.Message) {
	logger.Debug("Received device response: topic=%s, payload=%s", msg.Topic(), string(msg.Payload()))
	// TODO: 处理设备命令响应
}

// handleDeviceLog 处理设备日志消息
func (c *Client) handleDeviceLog(client mqtt.Client, msg mqtt.Message) {
	logger.Debug("Received device log: topic=%s, payload=%s", msg.Topic(), string(msg.Payload()))
	// TODO: 保存设备日志
}

// handleDeviceOTA 处理设备OTA消息
func (c *Client) handleDeviceOTA(client mqtt.Client, msg mqtt.Message) {
	logger.Debug("Received device OTA: topic=%s, payload=%s", msg.Topic(), string(msg.Payload()))
	// TODO: 处理OTA升级状态
}

// PublishDeviceCommand 发布设备命令
func (c *Client) PublishDeviceCommand(deviceSN string, command interface{}) error {
	topic := fmt.Sprintf("device/%s/command", deviceSN)
	return c.Publish(topic, 1, false, command)
}

// PublishDeviceUpdate 发布设备更新
func (c *Client) PublishDeviceUpdate(deviceSN string, data interface{}) error {
	topic := fmt.Sprintf("device/%s/update", deviceSN)
	return c.Publish(topic, 1, false, data)
}

// PublishOTAUpdate 发布OTA更新
func (c *Client) PublishOTAUpdate(deviceSN string, otaData interface{}) error {
	topic := fmt.Sprintf("device/%s/ota/update", deviceSN)
	return c.Publish(topic, 2, false, otaData)
}
