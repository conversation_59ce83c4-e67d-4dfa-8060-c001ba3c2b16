version: '3.8'

services:
  # 应用服务
  app:
    build: .
    container_name: electronic-price-tag-app
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=release
      - DATABASE_HOST=mysql
      - DATABASE_PORT=3306
      - DATABASE_USERNAME=root
      - DATABASE_PASSWORD=123456
      - DATABASE_DATABASE=electronic_price_tag
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - MQTT_BROKER=tcp://emqx:1883
    depends_on:
      - mysql
      - redis
      - emqx
    volumes:
      - ./logs:/root/logs
      - ./storage:/root/storage
      - ./uploads:/root/uploads
    networks:
      - electronic-price-tag-network
    restart: unless-stopped

  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: electronic-price-tag-mysql
    ports:
      - "3307:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=electronic_price_tag
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - electronic-price-tag-network
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: electronic-price-tag-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - electronic-price-tag-network
    restart: unless-stopped

  # EMQX MQTT Broker
  emqx:
    image: emqx/emqx:5.1
    container_name: electronic-price-tag-emqx
    ports:
      - "1883:1883"      # MQTT
      - "8083:8083"      # MQTT over WebSocket
      - "8084:8084"      # MQTT over SSL
      - "8883:8883"      # MQTT over SSL
      - "18083:18083"    # Dashboard
    environment:
      - EMQX_NAME=emqx
      - EMQX_HOST=127.0.0.1
      - EMQX_DASHBOARD__DEFAULT_USERNAME=admin
      - EMQX_DASHBOARD__DEFAULT_PASSWORD=public
    volumes:
      - emqx_data:/opt/emqx/data
      - emqx_log:/opt/emqx/log
    networks:
      - electronic-price-tag-network
    restart: unless-stopped

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: electronic-price-tag-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./static:/usr/share/nginx/html/static
      - ./uploads:/usr/share/nginx/html/uploads
    depends_on:
      - app
    networks:
      - electronic-price-tag-network
    restart: unless-stopped

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: electronic-price-tag-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - electronic-price-tag-network
    restart: unless-stopped

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: electronic-price-tag-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - electronic-price-tag-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  emqx_data:
  emqx_log:
  prometheus_data:
  grafana_data:

networks:
  electronic-price-tag-network:
    driver: bridge
