package service

import (
	"electronic-price-tag/pkg/mqtt"

	"github.com/casbin/casbin/v2"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// Services 服务集合
type Services struct {
	User     UserService
	Auth     AuthService
	Device   DeviceService
	Template TemplateService
	Product  ProductService
	OTA      OTAService
	MQTT     MQTTService
}

// NewServices 创建服务实例
func NewServices(db *gorm.DB, rdb *redis.Client, mqttClient *mqtt.Client, enforcer *casbin.Enforcer) *Services {
	return &Services{
		User:     NewUserService(db, rdb, enforcer),
		Auth:     NewAuthService(db, rdb),
		Device:   NewDeviceService(db, rdb, mqttClient),
		Template: NewTemplateService(db, rdb),
		Product:  NewProductService(db, rdb),
		OTA:      NewOTAService(db, rdb, mqttClient),
		MQTT:     NewMQTTService(mqttClient, db),
	}
}
