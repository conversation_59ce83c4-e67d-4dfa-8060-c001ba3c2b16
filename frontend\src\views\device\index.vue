<template>
  <div class="device-page">
    <!-- 搜索和操作栏 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="设备名称">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入设备名称或SN"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="设备状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="在线" :value="1" />
            <el-option label="离线" :value="0" />
            <el-option label="故障" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="action-buttons">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          添加设备
        </el-button>
        <el-button type="success" @click="handleBatchUpdate" :disabled="!selectedDevices.length">
          <el-icon><Refresh /></el-icon>
          批量更新
        </el-button>
        <el-button type="warning" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </el-card>

    <!-- 设备列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="deviceList"
        @selection-change="handleSelectionChange"
        stripe
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="sn" label="设备SN" width="120" />
        <el-table-column prop="name" label="设备名称" width="150" />
        <el-table-column prop="model" label="设备型号" width="100" />
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="电池电量" width="100">
          <template #default="{ row }">
            <el-progress
              :percentage="row.battery"
              :color="getBatteryColor(row.battery)"
              :stroke-width="6"
              text-inside
            />
          </template>
        </el-table-column>
        <el-table-column prop="rssi" label="信号强度" width="100">
          <template #default="{ row }">
            <span :class="getRssiClass(row.rssi)">{{ row.rssi }}dBm</span>
          </template>
        </el-table-column>
        <el-table-column prop="version" label="固件版本" width="100" />
        <el-table-column prop="last_seen" label="最后在线" width="150">
          <template #default="{ row }">
            {{ formatTime(row.last_seen) }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">
              详情
            </el-button>
            <el-button type="success" size="small" @click="handleControl(row)">
              控制
            </el-button>
            <el-dropdown @command="(command) => handleMoreAction(command, row)">
              <el-button type="info" size="small">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">编辑</el-dropdown-item>
                  <el-dropdown-item command="logs">日志</el-dropdown-item>
                  <el-dropdown-item command="ota">OTA升级</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 添加/编辑设备对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="deviceFormRef"
        :model="deviceForm"
        :rules="deviceRules"
        label-width="100px"
      >
        <el-form-item label="设备SN" prop="sn">
          <el-input v-model="deviceForm.sn" placeholder="请输入设备SN" />
        </el-form-item>
        <el-form-item label="设备名称" prop="name">
          <el-input v-model="deviceForm.name" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备型号" prop="model">
          <el-select v-model="deviceForm.model" placeholder="请选择设备型号">
            <el-option label="ESL-2.9" value="ESL-2.9" />
            <el-option label="ESL-4.2" value="ESL-4.2" />
            <el-option label="ESL-7.5" value="ESL-7.5" />
          </el-select>
        </el-form-item>
        <el-form-item label="关联商品" prop="product_id">
          <el-select v-model="deviceForm.product_id" placeholder="请选择商品" clearable>
            <el-option
              v-for="product in productList"
              :key="product.id"
              :label="product.name"
              :value="product.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="显示模板" prop="template_id">
          <el-select v-model="deviceForm.template_id" placeholder="请选择模板" clearable>
            <el-option
              v-for="template in templateList"
              :key="template.id"
              :label="template.name"
              :value="template.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 设备控制对话框 -->
    <el-dialog
      v-model="controlDialogVisible"
      title="设备控制"
      width="500px"
    >
      <el-form label-width="100px">
        <el-form-item label="控制命令">
          <el-select v-model="controlCommand" placeholder="请选择控制命令">
            <el-option label="刷新显示" value="refresh" />
            <el-option label="重启设备" value="reboot" />
            <el-option label="清屏" value="clear" />
            <el-option label="休眠" value="sleep" />
          </el-select>
        </el-form-item>
        <el-form-item label="命令参数" v-if="controlCommand === 'refresh'">
          <el-input
            v-model="controlParams"
            type="textarea"
            placeholder="请输入JSON格式的参数"
            :rows="3"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="controlDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSendCommand" :loading="commanding">
          发送命令
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const commanding = ref(false)
const dialogVisible = ref(false)
const controlDialogVisible = ref(false)
const deviceList = ref([])
const selectedDevices = ref([])
const productList = ref([])
const templateList = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: null
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 设备表单
const deviceForm = reactive({
  id: null,
  sn: '',
  name: '',
  model: '',
  product_id: null,
  template_id: null
})

// 表单验证规则
const deviceRules = {
  sn: [{ required: true, message: '请输入设备SN', trigger: 'blur' }],
  name: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
  model: [{ required: true, message: '请选择设备型号', trigger: 'change' }]
}

// 控制命令
const controlCommand = ref('')
const controlParams = ref('')
const currentDevice = ref(null)

// 计算属性和方法
const dialogTitle = computed(() => {
  return deviceForm.id ? '编辑设备' : '添加设备'
})

const getStatusType = (status) => {
  const types = { 0: 'info', 1: 'success', 2: 'danger' }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = { 0: '离线', 1: '在线', 2: '故障' }
  return texts[status] || '未知'
}

const getBatteryColor = (battery) => {
  if (battery > 50) return '#67c23a'
  if (battery > 20) return '#e6a23c'
  return '#f56c6c'
}

const getRssiClass = (rssi) => {
  if (rssi > -50) return 'rssi-excellent'
  if (rssi > -70) return 'rssi-good'
  if (rssi > -85) return 'rssi-fair'
  return 'rssi-poor'
}

const formatTime = (time) => {
  return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-'
}

// 事件处理
const handleSearch = () => {
  pagination.page = 1
  fetchDeviceList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: null
  })
  handleSearch()
}

const handleAdd = () => {
  Object.assign(deviceForm, {
    id: null,
    sn: '',
    name: '',
    model: '',
    product_id: null,
    template_id: null
  })
  dialogVisible.value = true
}

const handleView = (row) => {
  router.push(`/devices/${row.id}`)
}

const handleControl = (row) => {
  currentDevice.value = row
  controlCommand.value = ''
  controlParams.value = ''
  controlDialogVisible.value = true
}

const handleMoreAction = async (command, row) => {
  switch (command) {
    case 'edit':
      Object.assign(deviceForm, row)
      dialogVisible.value = true
      break
    case 'logs':
      router.push(`/devices/${row.id}/logs`)
      break
    case 'ota':
      router.push(`/ota/tasks/create?device_id=${row.id}`)
      break
    case 'delete':
      try {
        await ElMessageBox.confirm('确定要删除这个设备吗？', '提示', {
          type: 'warning'
        })
        await deleteDevice(row.id)
        ElMessage.success('删除成功')
        fetchDeviceList()
      } catch (error) {
        // 用户取消
      }
      break
  }
}

const handleSelectionChange = (selection) => {
  selectedDevices.value = selection
}

const handleBatchUpdate = () => {
  // 实现批量更新逻辑
  ElMessage.info('批量更新功能开发中...')
}

const handleExport = () => {
  // 实现导出功能
  ElMessage.info('导出功能开发中...')
}

const handleSizeChange = (size) => {
  pagination.pageSize = size
  fetchDeviceList()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchDeviceList()
}

const handleDialogClose = () => {
  // 重置表单
}

const handleSubmit = async () => {
  // 实现提交逻辑
  submitting.value = true
  try {
    if (deviceForm.id) {
      // 更新设备
      await updateDevice(deviceForm.id, deviceForm)
      ElMessage.success('更新成功')
    } else {
      // 创建设备
      await createDevice(deviceForm)
      ElMessage.success('创建成功')
    }
    dialogVisible.value = false
    fetchDeviceList()
  } catch (error) {
    ElMessage.error(error.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

const handleSendCommand = async () => {
  if (!controlCommand.value) {
    ElMessage.warning('请选择控制命令')
    return
  }
  
  commanding.value = true
  try {
    const command = {
      type: 'control',
      command: controlCommand.value,
      params: controlParams.value ? JSON.parse(controlParams.value) : {}
    }
    
    await sendDeviceCommand(currentDevice.value.id, command)
    ElMessage.success('命令发送成功')
    controlDialogVisible.value = false
  } catch (error) {
    ElMessage.error(error.message || '命令发送失败')
  } finally {
    commanding.value = false
  }
}

// API 调用（模拟）
const fetchDeviceList = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    deviceList.value = [
      {
        id: 1,
        sn: 'ESL001',
        name: '收银台价签',
        model: 'ESL-2.9',
        status: 1,
        battery: 85,
        rssi: -45,
        version: 'v1.2.3',
        last_seen: '2023-12-01 10:30:00',
        created_at: '2023-11-01 09:00:00'
      },
      {
        id: 2,
        sn: 'ESL002',
        name: '货架价签A',
        model: 'ESL-4.2',
        status: 0,
        battery: 45,
        rssi: -65,
        version: 'v1.2.2',
        last_seen: '2023-12-01 08:15:00',
        created_at: '2023-11-01 09:05:00'
      }
    ]
    
    pagination.total = 50
  } catch (error) {
    ElMessage.error('获取设备列表失败')
  } finally {
    loading.value = false
  }
}

const createDevice = async (data) => {
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 1000))
}

const updateDevice = async (id, data) => {
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 1000))
}

const deleteDevice = async (id) => {
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 1000))
}

const sendDeviceCommand = async (deviceId, command) => {
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 1000))
}

onMounted(() => {
  fetchDeviceList()
})
</script>

<style lang="scss" scoped>
.device-page {
  .search-card {
    margin-bottom: 16px;
    
    .el-form {
      margin-bottom: 16px;
    }
    
    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }
  
  .table-card {
    .pagination {
      margin-top: 16px;
      text-align: right;
    }
  }
  
  .rssi-excellent { color: #67c23a; }
  .rssi-good { color: #409eff; }
  .rssi-fair { color: #e6a23c; }
  .rssi-poor { color: #f56c6c; }
}
</style>
