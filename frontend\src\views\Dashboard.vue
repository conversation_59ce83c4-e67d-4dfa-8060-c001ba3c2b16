<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="16" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon device">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.deviceCount }}</div>
              <div class="stat-label">设备总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon online">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.onlineCount }}</div>
              <div class="stat-label">在线设备</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon product">
              <el-icon><Goods /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.productCount }}</div>
              <div class="stat-label">商品总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon template">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.templateCount }}</div>
              <div class="stat-label">模板总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="16" class="charts-row">
      <el-col :span="12">
        <el-card title="设备状态分布">
          <template #header>
            <span>设备状态分布</span>
          </template>
          <div class="chart-container">
            <v-chart :option="deviceStatusOption" />
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card title="设备在线趋势">
          <template #header>
            <span>设备在线趋势</span>
          </template>
          <div class="chart-container">
            <v-chart :option="onlineTrendOption" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-row :gutter="16" class="activity-row">
      <el-col :span="12">
        <el-card title="最近设备活动">
          <template #header>
            <span>最近设备活动</span>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="activity in recentActivities"
              :key="activity.id"
              :timestamp="activity.time"
              :type="activity.type"
            >
              {{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card title="系统公告">
          <template #header>
            <span>系统公告</span>
          </template>
          <el-empty v-if="announcements.length === 0" description="暂无公告" />
          <div v-else class="announcements">
            <div
              v-for="announcement in announcements"
              :key="announcement.id"
              class="announcement-item"
            >
              <div class="announcement-title">{{ announcement.title }}</div>
              <div class="announcement-time">{{ announcement.time }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  PieChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

// 统计数据
const stats = ref({
  deviceCount: 0,
  onlineCount: 0,
  productCount: 0,
  templateCount: 0
})

// 设备状态分布图表配置
const deviceStatusOption = ref({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '设备状态',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 0, name: '在线' },
        { value: 0, name: '离线' },
        { value: 0, name: '故障' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
})

// 在线趋势图表配置
const onlineTrendOption = ref({
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: []
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '在线设备数',
      type: 'line',
      data: [],
      smooth: true,
      itemStyle: {
        color: '#409EFF'
      }
    }
  ]
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    content: '设备 ESL001 上线',
    time: '2023-12-01 10:30:00',
    type: 'success'
  },
  {
    id: 2,
    content: '设备 ESL002 离线',
    time: '2023-12-01 10:25:00',
    type: 'warning'
  },
  {
    id: 3,
    content: '商品价格更新完成',
    time: '2023-12-01 10:20:00',
    type: 'primary'
  }
])

// 系统公告
const announcements = ref([
  {
    id: 1,
    title: '系统维护通知',
    time: '2023-12-01'
  },
  {
    id: 2,
    title: '新功能上线',
    time: '2023-11-30'
  }
])

// 获取统计数据
const fetchStats = async () => {
  try {
    // 这里应该调用实际的API
    // const response = await getDashboardStats()
    
    // 模拟数据
    stats.value = {
      deviceCount: 156,
      onlineCount: 142,
      productCount: 1234,
      templateCount: 25
    }
    
    // 更新图表数据
    deviceStatusOption.value.series[0].data = [
      { value: 142, name: '在线' },
      { value: 12, name: '离线' },
      { value: 2, name: '故障' }
    ]
    
    // 生成最近7天的在线趋势数据
    const dates = []
    const onlineData = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      dates.push(date.toLocaleDateString())
      onlineData.push(Math.floor(Math.random() * 20) + 130)
    }
    
    onlineTrendOption.value.xAxis.data = dates
    onlineTrendOption.value.series[0].data = onlineData
    
  } catch (error) {
    console.error('Failed to fetch stats:', error)
  }
}

onMounted(() => {
  fetchStats()
})
</script>

<style lang="scss" scoped>
.dashboard {
  .stats-row {
    margin-bottom: 16px;
  }
  
  .stat-card {
    .stat-content {
      display: flex;
      align-items: center;
      
      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        font-size: 24px;
        color: white;
        
        &.device {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        &.online {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        &.product {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        &.template {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }
      
      .stat-info {
        .stat-value {
          font-size: 28px;
          font-weight: 600;
          color: #303133;
          line-height: 1;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
  
  .charts-row {
    margin-bottom: 16px;
  }
  
  .chart-container {
    height: 300px;
  }
  
  .activity-row {
    .announcements {
      .announcement-item {
        padding: 12px 0;
        border-bottom: 1px solid #ebeef5;
        
        &:last-child {
          border-bottom: none;
        }
        
        .announcement-title {
          font-size: 14px;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .announcement-time {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}
</style>
