package controller

import (
	"electronic-price-tag/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type TemplateController struct {
	templateService service.TemplateService
}

type RenderTemplateRequest struct {
	Data map[string]interface{} `json:"data" binding:"required"`
}

func NewTemplateController(templateService service.TemplateService) *TemplateController {
	return &TemplateController{
		templateService: templateService,
	}
}

// GetTemplates 获取模板列表
// @Summary 获取模板列表
// @Description 分页获取模板列表
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Success 200 {object} service.TemplateListResponse
// @Failure 400 {object} map[string]interface{}
// @Router /api/templates [get]
func (c *TemplateController) GetTemplates(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	keyword := ctx.Query("keyword")

	// 获取当前用户的商户ID
	merchantID, _ := ctx.Get("merchant_id")

	response, err := c.templateService.GetTemplates(merchantID.(string), page, pageSize, keyword)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": response,
	})
}

// GetTemplateByID 根据ID获取模板
// @Summary 根据ID获取模板
// @Description 根据模板ID获取模板详细信息
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "模板ID"
// @Success 200 {object} model.Template
// @Failure 400 {object} map[string]interface{}
// @Router /api/templates/{id} [get]
func (c *TemplateController) GetTemplateByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的模板ID",
		})
		return
	}

	template, err := c.templateService.GetTemplateByID(uint(id))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code": 404,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": template,
	})
}

// CreateTemplate 创建模板
// @Summary 创建模板
// @Description 创建新模板
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body service.CreateTemplateRequest true "创建模板请求"
// @Success 200 {object} model.Template
// @Failure 400 {object} map[string]interface{}
// @Router /api/templates [post]
func (c *TemplateController) CreateTemplate(ctx *gin.Context) {
	var req service.CreateTemplateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	// 设置当前用户的商户ID
	merchantID, _ := ctx.Get("merchant_id")
	req.MerchantID = merchantID.(string)

	template, err := c.templateService.CreateTemplate(&req)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "创建成功",
		"data": template,
	})
}

// UpdateTemplate 更新模板
// @Summary 更新模板
// @Description 更新模板信息
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "模板ID"
// @Param request body service.UpdateTemplateRequest true "更新模板请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/templates/{id} [put]
func (c *TemplateController) UpdateTemplate(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的模板ID",
		})
		return
	}

	var req service.UpdateTemplateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	if err := c.templateService.UpdateTemplate(uint(id), &req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "更新成功",
	})
}

// DeleteTemplate 删除模板
// @Summary 删除模板
// @Description 删除模板
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "模板ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/templates/{id} [delete]
func (c *TemplateController) DeleteTemplate(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的模板ID",
		})
		return
	}

	if err := c.templateService.DeleteTemplate(uint(id)); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "删除成功",
	})
}

// GetTemplateElements 获取模板元素
// @Summary 获取模板元素
// @Description 获取模板的元素列表
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "模板ID"
// @Success 200 {object} []model.TemplateElement
// @Failure 400 {object} map[string]interface{}
// @Router /api/templates/{id}/elements [get]
func (c *TemplateController) GetTemplateElements(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的模板ID",
		})
		return
	}

	elements, err := c.templateService.GetTemplateElements(uint(id))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": elements,
	})
}

// CreateTemplateElement 创建模板元素
// @Summary 创建模板元素
// @Description 为模板创建新元素
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "模板ID"
// @Param request body service.CreateTemplateElementRequest true "创建模板元素请求"
// @Success 200 {object} model.TemplateElement
// @Failure 400 {object} map[string]interface{}
// @Router /api/templates/{id}/elements [post]
func (c *TemplateController) CreateTemplateElement(ctx *gin.Context) {
	templateID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的模板ID",
		})
		return
	}

	var req service.CreateTemplateElementRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	req.TemplateID = uint(templateID)

	element, err := c.templateService.CreateTemplateElement(&req)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "创建成功",
		"data": element,
	})
}

// UpdateTemplateElement 更新模板元素
// @Summary 更新模板元素
// @Description 更新模板元素信息
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "元素ID"
// @Param request body service.UpdateTemplateElementRequest true "更新模板元素请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/templates/elements/{id} [put]
func (c *TemplateController) UpdateTemplateElement(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的元素ID",
		})
		return
	}

	var req service.UpdateTemplateElementRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	if err := c.templateService.UpdateTemplateElement(uint(id), &req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "更新成功",
	})
}

// DeleteTemplateElement 删除模板元素
// @Summary 删除模板元素
// @Description 删除模板元素
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "元素ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/templates/elements/{id} [delete]
func (c *TemplateController) DeleteTemplateElement(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的元素ID",
		})
		return
	}

	if err := c.templateService.DeleteTemplateElement(uint(id)); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "删除成功",
	})
}

// RenderTemplate 渲染模板
// @Summary 渲染模板
// @Description 根据数据渲染模板
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "模板ID"
// @Param request body RenderTemplateRequest true "渲染数据"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/templates/{id}/render [post]
func (c *TemplateController) RenderTemplate(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的模板ID",
		})
		return
	}

	var req RenderTemplateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	result, err := c.templateService.RenderTemplate(uint(id), req.Data)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "渲染成功",
		"data": string(result),
	})
}

// GenerateEPD 生成EPD数据
// @Summary 生成EPD数据
// @Description 根据模板和数据生成EPD二进制数据
// @Tags 模板管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "模板ID"
// @Param request body RenderTemplateRequest true "渲染数据"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/templates/{id}/generate-epd [post]
func (c *TemplateController) GenerateEPD(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的模板ID",
		})
		return
	}

	var req RenderTemplateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	epdData, err := c.templateService.GenerateEPD(uint(id), req.Data)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "生成成功",
		"data": gin.H{
			"size": len(epdData),
			"data": epdData,
		},
	})
}
