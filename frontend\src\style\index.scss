// 全局样式文件

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// Element Plus 组件样式覆盖
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .el-card__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .el-card__body {
    padding: 20px;
  }
}

.el-button {
  border-radius: 6px;
  font-weight: 500;
  
  &.el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    
    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
  }
}

.el-input {
  .el-input__wrapper {
    border-radius: 6px;
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    
    &:hover {
      box-shadow: 0 0 0 1px #c0c4cc inset;
    }
    
    &.is-focus {
      box-shadow: 0 0 0 1px #409eff inset;
    }
  }
}

.el-select {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

.el-table {
  border-radius: 8px;
  overflow: hidden;
  
  .el-table__header {
    th {
      background: #fafafa;
      color: #606266;
      font-weight: 600;
    }
  }
  
  .el-table__row {
    &:hover {
      background: #f5f7fa;
    }
  }
}

.el-pagination {
  .el-pager li {
    border-radius: 4px;
    margin: 0 2px;
    
    &.is-active {
      background: #409eff;
      color: white;
    }
  }
  
  .btn-prev,
  .btn-next {
    border-radius: 4px;
  }
}

.el-dialog {
  border-radius: 12px;
  
  .el-dialog__header {
    padding: 20px 20px 10px;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
  }
  
  .el-dialog__body {
    padding: 10px 20px 20px;
  }
  
  .el-dialog__footer {
    padding: 10px 20px 20px;
  }
}

.el-message-box {
  border-radius: 12px;
}

.el-message {
  border-radius: 8px;
}

.el-notification {
  border-radius: 8px;
}

// 自定义工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.mb-16 {
  margin-bottom: 16px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-20 {
  margin-top: 20px;
}

.p-16 {
  padding: 16px;
}

.p-20 {
  padding: 20px;
}

// 状态颜色
.status-success {
  color: #67c23a;
}

.status-warning {
  color: #e6a23c;
}

.status-danger {
  color: #f56c6c;
}

.status-info {
  color: #909399;
}

// 响应式
@media (max-width: 768px) {
  .el-card .el-card__body {
    padding: 16px;
  }
  
  .el-table {
    font-size: 12px;
  }
  
  .el-button {
    padding: 8px 12px;
    font-size: 12px;
  }
}

// 加载动画
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  
  .loading-text {
    margin-left: 12px;
    color: #909399;
  }
}

// 空状态
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .empty-text {
    font-size: 14px;
  }
}

// 页面动画
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

// NProgress 样式覆盖
#nprogress {
  .bar {
    background: #409eff !important;
    height: 3px !important;
  }
  
  .peg {
    box-shadow: 0 0 10px #409eff, 0 0 5px #409eff !important;
  }
  
  .spinner {
    display: none !important;
  }
}
