<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-code">404</div>
      <div class="error-title">页面不存在</div>
      <div class="error-description">
        抱歉，您访问的页面不存在或已被删除
      </div>
      <div class="error-actions">
        <el-button type="primary" @click="goHome">
          <el-icon><HomeFilled /></el-icon>
          返回首页
        </el-button>
        <el-button @click="goBack">
          <el-icon><Back /></el-icon>
          返回上页
        </el-button>
      </div>
    </div>
    <div class="error-image">
      <img src="/404.svg" alt="404" />
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.error-page {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  
  .error-content {
    text-align: center;
    color: white;
    margin-right: 60px;
    
    .error-code {
      font-size: 120px;
      font-weight: 700;
      line-height: 1;
      margin-bottom: 20px;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
    
    .error-title {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 16px;
    }
    
    .error-description {
      font-size: 16px;
      margin-bottom: 40px;
      opacity: 0.9;
    }
    
    .error-actions {
      display: flex;
      gap: 16px;
      justify-content: center;
      
      .el-button {
        padding: 12px 24px;
        font-size: 16px;
        border-radius: 8px;
      }
    }
  }
  
  .error-image {
    img {
      width: 400px;
      height: auto;
    }
  }
}

@media (max-width: 768px) {
  .error-page {
    flex-direction: column;
    text-align: center;
    
    .error-content {
      margin-right: 0;
      margin-bottom: 40px;
      
      .error-code {
        font-size: 80px;
      }
      
      .error-title {
        font-size: 24px;
      }
      
      .error-actions {
        flex-direction: column;
        align-items: center;
      }
    }
    
    .error-image {
      img {
        width: 300px;
      }
    }
  }
}
</style>
