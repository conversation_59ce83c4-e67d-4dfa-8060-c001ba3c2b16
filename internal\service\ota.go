package service

import (
	"crypto/hmac"
	"crypto/sha256"
	"electronic-price-tag/internal/model"
	"electronic-price-tag/pkg/logger"
	"electronic-price-tag/pkg/mqtt"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"github.com/dgraph-io/ristretto"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type OTAService interface {
	GetFirmwares(page, pageSize int, model string) (*FirmwareListResponse, error)
	GetFirmwareByID(id uint) (*model.Firmware, error)
	UploadFirmware(req *UploadFirmwareRequest) (*model.Firmware, error)
	DeleteFirmware(id uint) error
	CreateOTATask(req *CreateOTATaskRequest) (*model.OTATask, error)
	GetOTATasks(merchantID string, page, pageSize int) (*OTATaskListResponse, error)
	GetOTATaskByID(id uint) (*model.OTATask, error)
	StartOTATask(id uint) error
	CancelOTATask(id uint) error
	GetOTARecords(taskID uint, page, pageSize int) (*OTARecordListResponse, error)
	GetUpdatePackage(deviceModel, currentVer string) ([]byte, error)
	HandleOTAProgress(deviceSN string, progress *OTAProgress) error
}

type otaService struct {
	db         *gorm.DB
	rdb        *redis.Client
	mqttClient *mqtt.Client
	diffCache  *ristretto.Cache
	secretKey  string
	storePath  string
}

type FirmwareListResponse struct {
	Total int               `json:"total"`
	List  []model.Firmware  `json:"list"`
}

type OTATaskListResponse struct {
	Total int             `json:"total"`
	List  []model.OTATask `json:"list"`
}

type OTARecordListResponse struct {
	Total int               `json:"total"`
	List  []model.OTARecord `json:"list"`
}

type UploadFirmwareRequest struct {
	Version     string `json:"version" binding:"required"`
	Model       string `json:"model" binding:"required"`
	FileName    string `json:"file_name" binding:"required"`
	FileData    []byte `json:"file_data" binding:"required"`
	Description string `json:"description"`
	ChangeLog   string `json:"change_log"`
	IsForced    bool   `json:"is_forced"`
}

type CreateOTATaskRequest struct {
	Name       string   `json:"name" binding:"required"`
	FirmwareID uint     `json:"firmware_id" binding:"required"`
	MerchantID string   `json:"merchant_id" binding:"required"`
	TargetType string   `json:"target_type" binding:"required"`
	TargetIDs  []string `json:"target_ids"`
	CreatedBy  uint     `json:"created_by" binding:"required"`
}

type OTAProgress struct {
	Status   int    `json:"status"`
	Progress int    `json:"progress"`
	ErrorMsg string `json:"error_msg"`
}

func NewOTAService(db *gorm.DB, rdb *redis.Client, mqttClient *mqtt.Client) OTAService {
	// 创建差分包缓存
	cache, _ := ristretto.NewCache(&ristretto.Config{
		NumCounters: 1e7,     // 10M
		MaxCost:     1 << 30, // 1GB
		BufferItems: 64,
	})

	return &otaService{
		db:         db,
		rdb:        rdb,
		mqttClient: mqttClient,
		diffCache:  cache,
		secretKey:  "ota-secret-key-2023", // 应该从配置读取
		storePath:  "./storage/firmware",   // 应该从配置读取
	}
}

// GetFirmwares 获取固件列表
func (s *otaService) GetFirmwares(page, pageSize int, deviceModel string) (*FirmwareListResponse, error) {
	var firmwares []model.Firmware
	var total int64

	query := s.db.Model(&model.Firmware{})

	// 设备型号过滤
	if deviceModel != "" {
		query = query.Where("model = ?", deviceModel)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count firmwares: %v", err)
		return nil, errors.New("获取固件总数失败")
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&firmwares).Error; err != nil {
		logger.Error("Failed to get firmwares: %v", err)
		return nil, errors.New("获取固件列表失败")
	}

	return &FirmwareListResponse{
		Total: int(total),
		List:  firmwares,
	}, nil
}

// GetFirmwareByID 根据ID获取固件
func (s *otaService) GetFirmwareByID(id uint) (*model.Firmware, error) {
	var firmware model.Firmware
	if err := s.db.Where("id = ?", id).First(&firmware).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("固件不存在")
		}
		logger.Error("Failed to get firmware by id: %v", err)
		return nil, errors.New("获取固件信息失败")
	}

	return &firmware, nil
}

// UploadFirmware 上传固件
func (s *otaService) UploadFirmware(req *UploadFirmwareRequest) (*model.Firmware, error) {
	// 检查版本是否已存在
	var existFirmware model.Firmware
	if err := s.db.Where("version = ? AND model = ?", req.Version, req.Model).First(&existFirmware).Error; err == nil {
		return nil, errors.New("该版本固件已存在")
	}

	// 确保存储目录存在
	if err := os.MkdirAll(s.storePath, 0755); err != nil {
		logger.Error("Failed to create storage directory: %v", err)
		return nil, errors.New("创建存储目录失败")
	}

	// 生成文件路径
	filePath := filepath.Join(s.storePath, fmt.Sprintf("%s_%s_%s", req.Model, req.Version, req.FileName))

	// 保存文件
	if err := os.WriteFile(filePath, req.FileData, 0644); err != nil {
		logger.Error("Failed to save firmware file: %v", err)
		return nil, errors.New("保存固件文件失败")
	}

	// 计算文件哈希
	hash := sha256.Sum256(req.FileData)
	fileHash := fmt.Sprintf("%x", hash)

	// 创建固件记录
	firmware := model.Firmware{
		Version:     req.Version,
		Model:       req.Model,
		FileName:    req.FileName,
		FilePath:    filePath,
		FileSize:    int64(len(req.FileData)),
		FileHash:    fileHash,
		Description: req.Description,
		ChangeLog:   req.ChangeLog,
		IsForced:    req.IsForced,
		IsActive:    true,
	}

	if err := s.db.Create(&firmware).Error; err != nil {
		// 删除已保存的文件
		os.Remove(filePath)
		logger.Error("Failed to create firmware: %v", err)
		return nil, errors.New("创建固件记录失败")
	}

	return &firmware, nil
}

// DeleteFirmware 删除固件
func (s *otaService) DeleteFirmware(id uint) error {
	// 检查固件是否存在
	var firmware model.Firmware
	if err := s.db.Where("id = ?", id).First(&firmware).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("固件不存在")
		}
		return errors.New("查询固件失败")
	}

	// 检查是否有OTA任务在使用此固件
	var taskCount int64
	if err := s.db.Model(&model.OTATask{}).Where("firmware_id = ?", id).Count(&taskCount).Error; err != nil {
		return errors.New("检查固件使用情况失败")
	}

	if taskCount > 0 {
		return errors.New("固件正在被OTA任务使用，无法删除")
	}

	// 删除文件
	if err := os.Remove(firmware.FilePath); err != nil {
		logger.Error("Failed to remove firmware file: %v", err)
	}

	// 软删除固件记录
	if err := s.db.Delete(&firmware).Error; err != nil {
		logger.Error("Failed to delete firmware: %v", err)
		return errors.New("删除固件失败")
	}

	return nil
}

// CreateOTATask 创建OTA任务
func (s *otaService) CreateOTATask(req *CreateOTATaskRequest) (*model.OTATask, error) {
	// 检查固件是否存在
	var firmware model.Firmware
	if err := s.db.Where("id = ?", req.FirmwareID).First(&firmware).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("固件不存在")
		}
		return nil, errors.New("查询固件失败")
	}

	// 序列化目标设备IDs
	targetIDsJSON, _ := json.Marshal(req.TargetIDs)

	// 创建OTA任务
	task := model.OTATask{
		Name:       req.Name,
		FirmwareID: req.FirmwareID,
		MerchantID: req.MerchantID,
		TargetType: req.TargetType,
		TargetIDs:  string(targetIDsJSON),
		Status:     0, // 待执行
		CreatedBy:  req.CreatedBy,
	}

	if err := s.db.Create(&task).Error; err != nil {
		logger.Error("Failed to create OTA task: %v", err)
		return nil, errors.New("创建OTA任务失败")
	}

	// 重新查询任务信息（包含关联数据）
	if err := s.db.Preload("Firmware").Preload("Creator").Where("id = ?", task.ID).First(&task).Error; err != nil {
		logger.Error("Failed to reload OTA task: %v", err)
	}

	return &task, nil
}

// GetOTATasks 获取OTA任务列表
func (s *otaService) GetOTATasks(merchantID string, page, pageSize int) (*OTATaskListResponse, error) {
	var tasks []model.OTATask
	var total int64

	query := s.db.Model(&model.OTATask{}).Preload("Firmware").Preload("Creator")

	// 商户过滤
	if merchantID != "" {
		query = query.Where("merchant_id = ?", merchantID)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count OTA tasks: %v", err)
		return nil, errors.New("获取OTA任务总数失败")
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&tasks).Error; err != nil {
		logger.Error("Failed to get OTA tasks: %v", err)
		return nil, errors.New("获取OTA任务列表失败")
	}

	return &OTATaskListResponse{
		Total: int(total),
		List:  tasks,
	}, nil
}

// GetOTATaskByID 根据ID获取OTA任务
func (s *otaService) GetOTATaskByID(id uint) (*model.OTATask, error) {
	var task model.OTATask
	if err := s.db.Preload("Firmware").Preload("Creator").Preload("Records").
		Where("id = ?", id).First(&task).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("OTA任务不存在")
		}
		logger.Error("Failed to get OTA task by id: %v", err)
		return nil, errors.New("获取OTA任务信息失败")
	}

	return &task, nil
}

// StartOTATask 启动OTA任务
func (s *otaService) StartOTATask(id uint) error {
	// 获取任务信息
	task, err := s.GetOTATaskByID(id)
	if err != nil {
		return err
	}

	if task.Status != 0 {
		return errors.New("任务状态不允许启动")
	}

	// 获取目标设备列表
	var targetIDs []string
	if err := json.Unmarshal([]byte(task.TargetIDs), &targetIDs); err != nil {
		return errors.New("解析目标设备失败")
	}

	// 查询设备列表
	var devices []model.Device
	if err := s.db.Where("id IN ?", targetIDs).Find(&devices).Error; err != nil {
		return errors.New("查询目标设备失败")
	}

	// 更新任务状态
	now := time.Now()
	updates := map[string]interface{}{
		"status":      1,
		"total_count": len(devices),
		"start_time":  &now,
	}

	if err := s.db.Model(task).Updates(updates).Error; err != nil {
		logger.Error("Failed to update OTA task status: %v", err)
		return errors.New("更新任务状态失败")
	}

	// 为每个设备创建升级记录
	for _, device := range devices {
		record := model.OTARecord{
			TaskID:      task.ID,
			DeviceID:    device.ID,
			FromVersion: device.Version,
			ToVersion:   task.Firmware.Version,
			Status:      0, // 待升级
		}

		if err := s.db.Create(&record).Error; err != nil {
			logger.Error("Failed to create OTA record for device %d: %v", device.ID, err)
			continue
		}

		// 发送OTA升级命令
		otaData := map[string]interface{}{
			"task_id":     task.ID,
			"record_id":   record.ID,
			"firmware_id": task.FirmwareID,
			"version":     task.Firmware.Version,
			"file_size":   task.Firmware.FileSize,
			"file_hash":   task.Firmware.FileHash,
			"is_forced":   task.Firmware.IsForced,
		}

		if err := s.mqttClient.PublishOTAUpdate(device.SN, otaData); err != nil {
			logger.Error("Failed to send OTA update to device %s: %v", device.SN, err)
			// 更新记录状态为失败
			s.db.Model(&record).Updates(map[string]interface{}{
				"status":    4,
				"error_msg": "发送OTA命令失败",
			})
		}
	}

	return nil
}

// CancelOTATask 取消OTA任务
func (s *otaService) CancelOTATask(id uint) error {
	// 获取任务信息
	task, err := s.GetOTATaskByID(id)
	if err != nil {
		return err
	}

	if task.Status != 1 {
		return errors.New("只能取消执行中的任务")
	}

	// 更新任务状态
	now := time.Now()
	updates := map[string]interface{}{
		"status":   3, // 已取消
		"end_time": &now,
	}

	if err := s.db.Model(task).Updates(updates).Error; err != nil {
		logger.Error("Failed to cancel OTA task: %v", err)
		return errors.New("取消任务失败")
	}

	// 取消所有待执行的升级记录
	s.db.Model(&model.OTARecord{}).Where("task_id = ? AND status IN ?", id, []int{0, 1, 2}).
		Updates(map[string]interface{}{
			"status":    4,
			"error_msg": "任务已取消",
		})

	return nil
}

// GetOTARecords 获取OTA升级记录
func (s *otaService) GetOTARecords(taskID uint, page, pageSize int) (*OTARecordListResponse, error) {
	var records []model.OTARecord
	var total int64

	query := s.db.Model(&model.OTARecord{}).Preload("Device").Where("task_id = ?", taskID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count OTA records: %v", err)
		return nil, errors.New("获取升级记录总数失败")
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&records).Error; err != nil {
		logger.Error("Failed to get OTA records: %v", err)
		return nil, errors.New("获取升级记录列表失败")
	}

	return &OTARecordListResponse{
		Total: int(total),
		List:  records,
	}, nil
}

// GetUpdatePackage 获取升级包（差分包）
func (s *otaService) GetUpdatePackage(deviceModel, currentVer string) ([]byte, error) {
	cacheKey := fmt.Sprintf("%s:%s", deviceModel, currentVer)

	// 从缓存获取差分包
	if pkg, ok := s.diffCache.Get(cacheKey); ok {
		return pkg.([]byte), nil
	}

	// 获取最新版本固件
	var latestFirmware model.Firmware
	if err := s.db.Where("model = ? AND is_active = ?", deviceModel, true).
		Order("created_at DESC").First(&latestFirmware).Error; err != nil {
		return nil, errors.New("未找到最新固件版本")
	}

	// 如果当前版本已是最新版本
	if currentVer == latestFirmware.Version {
		return nil, errors.New("当前已是最新版本")
	}

	// 读取最新固件文件
	targetFile, err := os.ReadFile(latestFirmware.FilePath)
	if err != nil {
		logger.Error("Failed to read target firmware file: %v", err)
		return nil, errors.New("读取目标固件文件失败")
	}

	// 简化实现：直接返回完整固件（实际应该生成差分包）
	// TODO: 实现BSDIFF差分算法
	diff := targetFile

	// 添加数字签名
	sign := hmac.New(sha256.New, []byte(s.secretKey))
	sign.Write(diff)
	signedDiff := append(diff, sign.Sum(nil)...)

	// 缓存差分包
	s.diffCache.Set(cacheKey, signedDiff, int64(len(signedDiff)))

	return signedDiff, nil
}

// HandleOTAProgress 处理OTA进度更新
func (s *otaService) HandleOTAProgress(deviceSN string, progress *OTAProgress) error {
	// 根据设备SN查找当前的OTA记录
	var device model.Device
	if err := s.db.Where("sn = ?", deviceSN).First(&device).Error; err != nil {
		return errors.New("设备不存在")
	}

	var record model.OTARecord
	if err := s.db.Where("device_id = ? AND status IN ?", device.ID, []int{0, 1, 2}).
		Order("created_at DESC").First(&record).Error; err != nil {
		return errors.New("未找到OTA升级记录")
	}

	// 更新升级记录
	updates := map[string]interface{}{
		"status":   progress.Status,
		"progress": progress.Progress,
	}

	if progress.ErrorMsg != "" {
		updates["error_msg"] = progress.ErrorMsg
	}

	if progress.Status == 3 || progress.Status == 4 { // 成功或失败
		now := time.Now()
		updates["end_time"] = &now

		// 如果升级成功，更新设备版本
		if progress.Status == 3 {
			s.db.Model(&device).Update("version", record.ToVersion)
		}
	}

	if err := s.db.Model(&record).Updates(updates).Error; err != nil {
		logger.Error("Failed to update OTA record: %v", err)
		return errors.New("更新升级记录失败")
	}

	// 更新任务统计
	s.updateTaskStatistics(record.TaskID)

	return nil
}

// updateTaskStatistics 更新任务统计信息
func (s *otaService) updateTaskStatistics(taskID uint) {
	var successCount, failedCount int64

	s.db.Model(&model.OTARecord{}).Where("task_id = ? AND status = ?", taskID, 3).Count(&successCount)
	s.db.Model(&model.OTARecord{}).Where("task_id = ? AND status = ?", taskID, 4).Count(&failedCount)

	updates := map[string]interface{}{
		"success_count": successCount,
		"failed_count":  failedCount,
	}

	// 检查任务是否完成
	var totalCount int64
	s.db.Model(&model.OTARecord{}).Where("task_id = ?", taskID).Count(&totalCount)

	if successCount+failedCount == totalCount {
		now := time.Now()
		updates["status"] = 2 // 已完成
		updates["end_time"] = &now
	}

	s.db.Model(&model.OTATask{}).Where("id = ?", taskID).Updates(updates)
}
