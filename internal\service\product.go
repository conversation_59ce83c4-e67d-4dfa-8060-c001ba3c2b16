package service

import (
	"electronic-price-tag/internal/model"
	"electronic-price-tag/pkg/logger"
	"errors"
	"fmt"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type ProductService interface {
	GetProducts(merchantID string, page, pageSize int, keyword string, categoryID uint) (*ProductListResponse, error)
	GetProductByID(id uint) (*model.Product, error)
	CreateProduct(req *CreateProductRequest) (*model.Product, error)
	UpdateProduct(id uint, req *UpdateProductRequest) error
	DeleteProduct(id uint) error
	UpdateProductPrice(id uint, newPrice int, reason string, operatorID uint) error
	GetProductPriceLogs(productID uint, page, pageSize int) (*ProductPriceLogListResponse, error)
	GetCategories(merchantID string) ([]model.ProductCategory, error)
	CreateCategory(req *CreateCategoryRequest) (*model.ProductCategory, error)
	UpdateCategory(id uint, req *UpdateCategoryRequest) error
	DeleteCategory(id uint) error
}

type productService struct {
	db  *gorm.DB
	rdb *redis.Client
}

type ProductListResponse struct {
	Total int             `json:"total"`
	List  []model.Product `json:"list"`
}

type ProductPriceLogListResponse struct {
	Total int                     `json:"total"`
	List  []model.ProductPriceLog `json:"list"`
}

type CreateProductRequest struct {
	Name          string `json:"name" binding:"required"`
	Code          string `json:"code" binding:"required"`
	Barcode       string `json:"barcode"`
	CategoryID    uint   `json:"category_id"`
	MerchantID    string `json:"merchant_id" binding:"required"`
	Brand         string `json:"brand"`
	Model         string `json:"model"`
	Spec          string `json:"spec"`
	Unit          string `json:"unit"`
	Price         int    `json:"price" binding:"required"`
	OriginalPrice int    `json:"original_price"`
	CostPrice     int    `json:"cost_price"`
	Stock         int    `json:"stock"`
	MinStock      int    `json:"min_stock"`
	Image         string `json:"image"`
	Images        string `json:"images"`
	Description   string `json:"description"`
}

type UpdateProductRequest struct {
	Name          string `json:"name"`
	Code          string `json:"code"`
	Barcode       string `json:"barcode"`
	CategoryID    *uint  `json:"category_id"`
	Brand         string `json:"brand"`
	Model         string `json:"model"`
	Spec          string `json:"spec"`
	Unit          string `json:"unit"`
	Price         *int   `json:"price"`
	OriginalPrice *int   `json:"original_price"`
	CostPrice     *int   `json:"cost_price"`
	Stock         *int   `json:"stock"`
	MinStock      *int   `json:"min_stock"`
	Image         string `json:"image"`
	Images        string `json:"images"`
	Description   string `json:"description"`
	Status        *int   `json:"status"`
}

type CreateCategoryRequest struct {
	Name        string `json:"name" binding:"required"`
	Code        string `json:"code" binding:"required"`
	MerchantID  string `json:"merchant_id" binding:"required"`
	ParentID    uint   `json:"parent_id"`
	Sort        int    `json:"sort"`
	Icon        string `json:"icon"`
	Description string `json:"description"`
}

type UpdateCategoryRequest struct {
	Name        string `json:"name"`
	Code        string `json:"code"`
	ParentID    *uint  `json:"parent_id"`
	Sort        *int   `json:"sort"`
	Icon        string `json:"icon"`
	Description string `json:"description"`
	Status      *int   `json:"status"`
}

func NewProductService(db *gorm.DB, rdb *redis.Client) ProductService {
	return &productService{
		db:  db,
		rdb: rdb,
	}
}

// GetProducts 获取商品列表
func (s *productService) GetProducts(merchantID string, page, pageSize int, keyword string, categoryID uint) (*ProductListResponse, error) {
	var products []model.Product
	var total int64

	query := s.db.Model(&model.Product{}).Preload("Category")

	// 商户过滤
	if merchantID != "" {
		query = query.Where("merchant_id = ?", merchantID)
	}

	// 分类过滤
	if categoryID > 0 {
		query = query.Where("category_id = ?", categoryID)
	}

	// 关键词搜索
	if keyword != "" {
		query = query.Where("name LIKE ? OR code LIKE ? OR barcode LIKE ?",
			fmt.Sprintf("%%%s%%", keyword),
			fmt.Sprintf("%%%s%%", keyword),
			fmt.Sprintf("%%%s%%", keyword))
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count products: %v", err)
		return nil, errors.New("获取商品总数失败")
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&products).Error; err != nil {
		logger.Error("Failed to get products: %v", err)
		return nil, errors.New("获取商品列表失败")
	}

	return &ProductListResponse{
		Total: int(total),
		List:  products,
	}, nil
}

// GetProductByID 根据ID获取商品
func (s *productService) GetProductByID(id uint) (*model.Product, error) {
	var product model.Product
	if err := s.db.Preload("Category").Where("id = ?", id).First(&product).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("商品不存在")
		}
		logger.Error("Failed to get product by id: %v", err)
		return nil, errors.New("获取商品信息失败")
	}

	return &product, nil
}

// CreateProduct 创建商品
func (s *productService) CreateProduct(req *CreateProductRequest) (*model.Product, error) {
	// 检查商品编码是否已存在
	var existProduct model.Product
	if err := s.db.Where("code = ? AND merchant_id = ?", req.Code, req.MerchantID).First(&existProduct).Error; err == nil {
		return nil, errors.New("商品编码已存在")
	}

	// 检查条形码是否已存在
	if req.Barcode != "" {
		if err := s.db.Where("barcode = ? AND merchant_id = ?", req.Barcode, req.MerchantID).First(&existProduct).Error; err == nil {
			return nil, errors.New("商品条形码已存在")
		}
	}

	// 创建商品
	product := model.Product{
		Name:          req.Name,
		Code:          req.Code,
		Barcode:       req.Barcode,
		CategoryID:    req.CategoryID,
		MerchantID:    req.MerchantID,
		Brand:         req.Brand,
		Model:         req.Model,
		Spec:          req.Spec,
		Unit:          req.Unit,
		Price:         req.Price,
		OriginalPrice: req.OriginalPrice,
		CostPrice:     req.CostPrice,
		Stock:         req.Stock,
		MinStock:      req.MinStock,
		Image:         req.Image,
		Images:        req.Images,
		Description:   req.Description,
		Status:        1,
	}

	if err := s.db.Create(&product).Error; err != nil {
		logger.Error("Failed to create product: %v", err)
		return nil, errors.New("创建商品失败")
	}

	// 重新查询商品信息（包含分类）
	if err := s.db.Preload("Category").Where("id = ?", product.ID).First(&product).Error; err != nil {
		logger.Error("Failed to reload product: %v", err)
	}

	return &product, nil
}

// UpdateProduct 更新商品
func (s *productService) UpdateProduct(id uint, req *UpdateProductRequest) error {
	// 检查商品是否存在
	var product model.Product
	if err := s.db.Where("id = ?", id).First(&product).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("商品不存在")
		}
		return errors.New("查询商品失败")
	}

	// 检查商品编码是否已被其他商品使用
	if req.Code != "" && req.Code != product.Code {
		var existProduct model.Product
		if err := s.db.Where("code = ? AND merchant_id = ? AND id != ?", req.Code, product.MerchantID, id).
			First(&existProduct).Error; err == nil {
			return errors.New("商品编码已被其他商品使用")
		}
	}

	// 检查条形码是否已被其他商品使用
	if req.Barcode != "" && req.Barcode != product.Barcode {
		var existProduct model.Product
		if err := s.db.Where("barcode = ? AND merchant_id = ? AND id != ?", req.Barcode, product.MerchantID, id).
			First(&existProduct).Error; err == nil {
			return errors.New("商品条形码已被其他商品使用")
		}
	}

	// 记录价格变更
	if req.Price != nil && *req.Price != product.Price {
		priceLog := model.ProductPriceLog{
			ProductID:  product.ID,
			OldPrice:   product.Price,
			NewPrice:   *req.Price,
			ChangeType: "manual",
			Reason:     "手动修改",
		}
		s.db.Create(&priceLog)
	}

	// 更新商品信息
	updates := map[string]interface{}{}
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Code != "" {
		updates["code"] = req.Code
	}
	if req.Barcode != "" {
		updates["barcode"] = req.Barcode
	}
	if req.CategoryID != nil {
		updates["category_id"] = *req.CategoryID
	}
	if req.Brand != "" {
		updates["brand"] = req.Brand
	}
	if req.Model != "" {
		updates["model"] = req.Model
	}
	if req.Spec != "" {
		updates["spec"] = req.Spec
	}
	if req.Unit != "" {
		updates["unit"] = req.Unit
	}
	if req.Price != nil {
		updates["price"] = *req.Price
	}
	if req.OriginalPrice != nil {
		updates["original_price"] = *req.OriginalPrice
	}
	if req.CostPrice != nil {
		updates["cost_price"] = *req.CostPrice
	}
	if req.Stock != nil {
		updates["stock"] = *req.Stock
	}
	if req.MinStock != nil {
		updates["min_stock"] = *req.MinStock
	}
	if req.Image != "" {
		updates["image"] = req.Image
	}
	if req.Images != "" {
		updates["images"] = req.Images
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	if len(updates) > 0 {
		if err := s.db.Model(&product).Updates(updates).Error; err != nil {
			logger.Error("Failed to update product: %v", err)
			return errors.New("更新商品失败")
		}
	}

	return nil
}

// DeleteProduct 删除商品
func (s *productService) DeleteProduct(id uint) error {
	// 检查商品是否存在
	var product model.Product
	if err := s.db.Where("id = ?", id).First(&product).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("商品不存在")
		}
		return errors.New("查询商品失败")
	}

	// 检查是否有设备在使用此商品
	var deviceCount int64
	if err := s.db.Model(&model.Device{}).Where("product_id = ?", id).Count(&deviceCount).Error; err != nil {
		return errors.New("检查商品使用情况失败")
	}

	if deviceCount > 0 {
		return errors.New("商品正在被设备使用，无法删除")
	}

	// 软删除商品
	if err := s.db.Delete(&product).Error; err != nil {
		logger.Error("Failed to delete product: %v", err)
		return errors.New("删除商品失败")
	}

	return nil
}

// UpdateProductPrice 更新商品价格
func (s *productService) UpdateProductPrice(id uint, newPrice int, reason string, operatorID uint) error {
	// 获取商品信息
	var product model.Product
	if err := s.db.Where("id = ?", id).First(&product).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("商品不存在")
		}
		return errors.New("查询商品失败")
	}

	// 记录价格变更日志
	priceLog := model.ProductPriceLog{
		ProductID:  product.ID,
		OldPrice:   product.Price,
		NewPrice:   newPrice,
		ChangeType: "manual",
		Reason:     reason,
		OperatorID: operatorID,
	}

	if err := s.db.Create(&priceLog).Error; err != nil {
		logger.Error("Failed to create price log: %v", err)
	}

	// 更新商品价格
	if err := s.db.Model(&product).Update("price", newPrice).Error; err != nil {
		logger.Error("Failed to update product price: %v", err)
		return errors.New("更新商品价格失败")
	}

	return nil
}

// GetProductPriceLogs 获取商品价格变更日志
func (s *productService) GetProductPriceLogs(productID uint, page, pageSize int) (*ProductPriceLogListResponse, error) {
	var logs []model.ProductPriceLog
	var total int64

	query := s.db.Model(&model.ProductPriceLog{}).Preload("Operator").Where("product_id = ?", productID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count price logs: %v", err)
		return nil, errors.New("获取价格日志总数失败")
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&logs).Error; err != nil {
		logger.Error("Failed to get price logs: %v", err)
		return nil, errors.New("获取价格日志列表失败")
	}

	return &ProductPriceLogListResponse{
		Total: int(total),
		List:  logs,
	}, nil
}

// GetCategories 获取商品分类列表
func (s *productService) GetCategories(merchantID string) ([]model.ProductCategory, error) {
	var categories []model.ProductCategory
	query := s.db.Model(&model.ProductCategory{}).Preload("Children")

	if merchantID != "" {
		query = query.Where("merchant_id = ?", merchantID)
	}

	if err := query.Where("parent_id = 0").Order("sort ASC").Find(&categories).Error; err != nil {
		logger.Error("Failed to get categories: %v", err)
		return nil, errors.New("获取分类列表失败")
	}

	return categories, nil
}

// CreateCategory 创建商品分类
func (s *productService) CreateCategory(req *CreateCategoryRequest) (*model.ProductCategory, error) {
	// 检查分类编码是否已存在
	var existCategory model.ProductCategory
	if err := s.db.Where("code = ? AND merchant_id = ?", req.Code, req.MerchantID).First(&existCategory).Error; err == nil {
		return nil, errors.New("分类编码已存在")
	}

	// 创建分类
	category := model.ProductCategory{
		Name:        req.Name,
		Code:        req.Code,
		MerchantID:  req.MerchantID,
		ParentID:    req.ParentID,
		Sort:        req.Sort,
		Icon:        req.Icon,
		Description: req.Description,
		Status:      1,
	}

	if err := s.db.Create(&category).Error; err != nil {
		logger.Error("Failed to create category: %v", err)
		return nil, errors.New("创建分类失败")
	}

	return &category, nil
}

// UpdateCategory 更新商品分类
func (s *productService) UpdateCategory(id uint, req *UpdateCategoryRequest) error {
	// 检查分类是否存在
	var category model.ProductCategory
	if err := s.db.Where("id = ?", id).First(&category).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("分类不存在")
		}
		return errors.New("查询分类失败")
	}

	// 检查分类编码是否已被其他分类使用
	if req.Code != "" && req.Code != category.Code {
		var existCategory model.ProductCategory
		if err := s.db.Where("code = ? AND merchant_id = ? AND id != ?", req.Code, category.MerchantID, id).
			First(&existCategory).Error; err == nil {
			return errors.New("分类编码已被其他分类使用")
		}
	}

	// 更新分类信息
	updates := map[string]interface{}{}
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Code != "" {
		updates["code"] = req.Code
	}
	if req.ParentID != nil {
		updates["parent_id"] = *req.ParentID
	}
	if req.Sort != nil {
		updates["sort"] = *req.Sort
	}
	if req.Icon != "" {
		updates["icon"] = req.Icon
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	if len(updates) > 0 {
		if err := s.db.Model(&category).Updates(updates).Error; err != nil {
			logger.Error("Failed to update category: %v", err)
			return errors.New("更新分类失败")
		}
	}

	return nil
}

// DeleteCategory 删除商品分类
func (s *productService) DeleteCategory(id uint) error {
	// 检查分类是否存在
	var category model.ProductCategory
	if err := s.db.Where("id = ?", id).First(&category).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("分类不存在")
		}
		return errors.New("查询分类失败")
	}

	// 检查是否有子分类
	var childCount int64
	if err := s.db.Model(&model.ProductCategory{}).Where("parent_id = ?", id).Count(&childCount).Error; err != nil {
		return errors.New("检查子分类失败")
	}

	if childCount > 0 {
		return errors.New("分类下存在子分类，无法删除")
	}

	// 检查是否有商品使用此分类
	var productCount int64
	if err := s.db.Model(&model.Product{}).Where("category_id = ?", id).Count(&productCount).Error; err != nil {
		return errors.New("检查分类使用情况失败")
	}

	if productCount > 0 {
		return errors.New("分类下存在商品，无法删除")
	}

	// 软删除分类
	if err := s.db.Delete(&category).Error; err != nil {
		logger.Error("Failed to delete category: %v", err)
		return errors.New("删除分类失败")
	}

	return nil
}
