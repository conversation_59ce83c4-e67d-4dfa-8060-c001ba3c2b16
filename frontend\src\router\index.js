import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

NProgress.configure({ showSpinner: false })

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录', requiresAuth: false }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '仪表盘', icon: 'DataBoard' }
      },
      {
        path: '/devices',
        name: 'Devices',
        component: () => import('@/views/device/index.vue'),
        meta: { title: '设备管理', icon: 'Monitor' }
      },
      {
        path: '/devices/:id',
        name: 'DeviceDetail',
        component: () => import('@/views/device/detail.vue'),
        meta: { title: '设备详情', hidden: true }
      },
      {
        path: '/templates',
        name: 'Templates',
        component: () => import('@/views/template/index.vue'),
        meta: { title: '模板管理', icon: 'Document' }
      },
      {
        path: '/templates/designer/:id?',
        name: 'TemplateDesigner',
        component: () => import('@/views/template/designer.vue'),
        meta: { title: '模板设计器', hidden: true }
      },
      {
        path: '/products',
        name: 'Products',
        component: () => import('@/views/product/index.vue'),
        meta: { title: '商品管理', icon: 'Goods' }
      },
      {
        path: '/ota',
        name: 'OTA',
        component: () => import('@/views/ota/index.vue'),
        meta: { title: 'OTA管理', icon: 'Upload' }
      },
      {
        path: '/users',
        name: 'Users',
        component: () => import('@/views/system/user.vue'),
        meta: { title: '用户管理', icon: 'User' }
      },
      {
        path: '/roles',
        name: 'Roles',
        component: () => import('@/views/system/role.vue'),
        meta: { title: '角色管理', icon: 'UserFilled' }
      },
      {
        path: '/logs',
        name: 'Logs',
        component: () => import('@/views/system/log.vue'),
        meta: { title: '系统日志', icon: 'Document' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/404.vue'),
    meta: { title: '页面不存在' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()
  
  const userStore = useUserStore()
  const token = userStore.token
  
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 电子价签管理系统` : '电子价签管理系统'
  
  if (to.meta.requiresAuth !== false) {
    if (!token) {
      next('/login')
      return
    }
    
    // 检查用户信息
    if (!userStore.userInfo) {
      try {
        await userStore.getUserInfo()
      } catch (error) {
        userStore.logout()
        next('/login')
        return
      }
    }
  } else {
    // 已登录用户访问登录页，重定向到首页
    if (token && to.path === '/login') {
      next('/')
      return
    }
  }
  
  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
