#!/bin/bash

# 电子价签系统启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查 Go
    if ! command -v go &> /dev/null; then
        log_error "Go 未安装，请先安装 Go 1.21+"
        exit 1
    fi
    
    # 检查 Go 版本
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    log_info "Go 版本: $GO_VERSION"
    
    # 检查 Docker (可选)
    if command -v docker &> /dev/null; then
        log_info "Docker 已安装"
    else
        log_warn "Docker 未安装，将使用本地模式"
    fi
    
    # 检查 MySQL
    if command -v mysql &> /dev/null; then
        log_info "MySQL 客户端已安装"
    else
        log_warn "MySQL 客户端未安装"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    mkdir -p storage/firmware
    mkdir -p uploads
    mkdir -p static
    mkdir -p tmp
    
    log_info "目录创建完成"
}

# 安装 Go 依赖
install_dependencies() {
    log_info "安装 Go 依赖..."
    
    if [ ! -f "go.mod" ]; then
        log_error "go.mod 文件不存在"
        exit 1
    fi
    
    go mod download
    go mod tidy
    
    log_info "依赖安装完成"
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    if [ ! -f "configs/config.yaml" ]; then
        log_error "配置文件 configs/config.yaml 不存在"
        exit 1
    fi
    
    log_info "配置文件检查完成"
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    # 这里可以添加数据库连接检查逻辑
    # 暂时跳过，由应用程序启动时检查
    
    log_info "数据库检查完成"
}

# 构建应用
build_app() {
    log_info "构建应用..."
    
    make build
    
    if [ $? -eq 0 ]; then
        log_info "应用构建成功"
    else
        log_error "应用构建失败"
        exit 1
    fi
}

# 启动应用
start_app() {
    log_info "启动电子价签系统..."
    
    # 检查是否已有进程在运行
    if pgrep -f "electronic-price-tag-server" > /dev/null; then
        log_warn "应用已在运行，正在重启..."
        make stop
        sleep 2
    fi
    
    # 启动应用
    nohup ./build/electronic-price-tag-server > logs/app.log 2>&1 &
    
    # 等待启动
    sleep 3
    
    # 检查是否启动成功
    if pgrep -f "electronic-price-tag-server" > /dev/null; then
        log_info "应用启动成功！"
        log_info "API 地址: http://localhost:8080"
        log_info "健康检查: http://localhost:8080/health"
        log_info "日志文件: logs/app.log"
    else
        log_error "应用启动失败，请检查日志"
        exit 1
    fi
}

# 显示状态
show_status() {
    log_info "系统状态:"
    
    if pgrep -f "electronic-price-tag-server" > /dev/null; then
        echo -e "  应用状态: ${GREEN}运行中${NC}"
        echo -e "  进程ID: $(pgrep -f 'electronic-price-tag-server')"
    else
        echo -e "  应用状态: ${RED}未运行${NC}"
    fi
    
    # 检查端口
    if netstat -tuln 2>/dev/null | grep -q ":8080 "; then
        echo -e "  端口 8080: ${GREEN}监听中${NC}"
    else
        echo -e "  端口 8080: ${RED}未监听${NC}"
    fi
}

# 显示帮助
show_help() {
    echo "电子价签系统启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start     启动系统"
    echo "  stop      停止系统"
    echo "  restart   重启系统"
    echo "  status    显示状态"
    echo "  build     仅构建"
    echo "  dev       开发模式启动"
    echo "  help      显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 start    # 启动系统"
    echo "  $0 dev      # 开发模式启动"
}

# 主函数
main() {
    case "${1:-start}" in
        "start")
            log_info "开始启动电子价签系统..."
            check_dependencies
            create_directories
            install_dependencies
            check_config
            build_app
            start_app
            show_status
            ;;
        "stop")
            log_info "停止系统..."
            make stop
            ;;
        "restart")
            log_info "重启系统..."
            make restart
            show_status
            ;;
        "status")
            show_status
            ;;
        "build")
            log_info "构建应用..."
            check_dependencies
            install_dependencies
            build_app
            ;;
        "dev")
            log_info "开发模式启动..."
            check_dependencies
            create_directories
            install_dependencies
            check_config
            make dev
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
