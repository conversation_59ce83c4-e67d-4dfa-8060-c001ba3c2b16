package controller

import (
	"electronic-price-tag/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

type DeviceController struct {
	deviceService service.DeviceService
}

type RegisterDeviceRequest struct {
	SN        string `json:"sn" binding:"required"`
	SecretKey string `json:"secret_key" binding:"required"`
}

type DeviceHeartbeatRequest struct {
	SN      string `json:"sn" binding:"required"`
	Battery int    `json:"battery"`
	RSSI    int    `json:"rssi"`
	Status  int    `json:"status"`
}

type BatchUpdateDisplayRequest struct {
	DeviceIDs  []uint `json:"device_ids" binding:"required"`
	TemplateID uint   `json:"template_id" binding:"required"`
}

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许跨域
	},
}

func NewDeviceController(deviceService service.DeviceService) *DeviceController {
	return &DeviceController{
		deviceService: deviceService,
	}
}

// GetDevices 获取设备列表
// @Summary 获取设备列表
// @Description 分页获取设备列表
// @Tags 设备管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Success 200 {object} service.DeviceListResponse
// @Failure 400 {object} map[string]interface{}
// @Router /api/devices [get]
func (c *DeviceController) GetDevices(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	keyword := ctx.Query("keyword")

	// 获取当前用户的商户ID
	merchantID, _ := ctx.Get("merchant_id")

	response, err := c.deviceService.GetDevices(merchantID.(string), page, pageSize, keyword)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": response,
	})
}

// GetDeviceByID 根据ID获取设备
// @Summary 根据ID获取设备
// @Description 根据设备ID获取设备详细信息
// @Tags 设备管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "设备ID"
// @Success 200 {object} model.Device
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Router /api/devices/{id} [get]
func (c *DeviceController) GetDeviceByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的设备ID",
		})
		return
	}

	device, err := c.deviceService.GetDeviceByID(uint(id))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code": 404,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": device,
	})
}

// CreateDevice 创建设备
// @Summary 创建设备
// @Description 创建新设备
// @Tags 设备管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body service.CreateDeviceRequest true "创建设备请求"
// @Success 200 {object} model.Device
// @Failure 400 {object} map[string]interface{}
// @Router /api/devices [post]
func (c *DeviceController) CreateDevice(ctx *gin.Context) {
	var req service.CreateDeviceRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	// 设置当前用户的商户ID
	merchantID, _ := ctx.Get("merchant_id")
	req.MerchantID = merchantID.(string)

	device, err := c.deviceService.CreateDevice(&req)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "创建成功",
		"data": device,
	})
}

// UpdateDevice 更新设备
// @Summary 更新设备
// @Description 更新设备信息
// @Tags 设备管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "设备ID"
// @Param request body service.UpdateDeviceRequest true "更新设备请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/devices/{id} [put]
func (c *DeviceController) UpdateDevice(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的设备ID",
		})
		return
	}

	var req service.UpdateDeviceRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	if err := c.deviceService.UpdateDevice(uint(id), &req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "更新成功",
	})
}

// DeleteDevice 删除设备
// @Summary 删除设备
// @Description 删除设备
// @Tags 设备管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "设备ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/devices/{id} [delete]
func (c *DeviceController) DeleteDevice(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的设备ID",
		})
		return
	}

	if err := c.deviceService.DeleteDevice(uint(id)); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "删除成功",
	})
}

// SendCommand 发送设备命令
// @Summary 发送设备命令
// @Description 向设备发送控制命令
// @Tags 设备管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "设备ID"
// @Param request body service.DeviceCommand true "设备命令"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/devices/{id}/commands [post]
func (c *DeviceController) SendCommand(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的设备ID",
		})
		return
	}

	var command service.DeviceCommand
	if err := ctx.ShouldBindJSON(&command); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	if err := c.deviceService.SendCommand(uint(id), &command); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "命令发送成功",
	})
}

// GetDeviceCommands 获取设备命令列表
// @Summary 获取设备命令列表
// @Description 获取设备的命令执行记录
// @Tags 设备管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "设备ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} service.DeviceCommandListResponse
// @Failure 400 {object} map[string]interface{}
// @Router /api/devices/{id}/commands [get]
func (c *DeviceController) GetDeviceCommands(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的设备ID",
		})
		return
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	response, err := c.deviceService.GetDeviceCommands(uint(id), page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": response,
	})
}

// GetDeviceLogs 获取设备日志
// @Summary 获取设备日志
// @Description 获取设备的日志记录
// @Tags 设备管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "设备ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param type query string false "日志类型"
// @Success 200 {object} service.DeviceLogListResponse
// @Failure 400 {object} map[string]interface{}
// @Router /api/devices/{id}/logs [get]
func (c *DeviceController) GetDeviceLogs(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的设备ID",
		})
		return
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	logType := ctx.Query("type")

	response, err := c.deviceService.GetDeviceLogs(uint(id), page, pageSize, logType)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": response,
	})
}

// BatchUpdateDisplay 批量更新显示
// @Summary 批量更新显示
// @Description 批量更新设备显示内容
// @Tags 设备管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body BatchUpdateDisplayRequest true "批量更新请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/devices/batch-update [post]
func (c *DeviceController) BatchUpdateDisplay(ctx *gin.Context) {
	var req BatchUpdateDisplayRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	if err := c.deviceService.BatchUpdateDisplay(req.DeviceIDs, req.TemplateID); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "批量更新成功",
	})
}

// ActivateDevice 激活设备
// @Summary 激活设备
// @Description 设备激活接口
// @Tags 设备管理
// @Accept json
// @Produce json
// @Param request body RegisterDeviceRequest true "激活设备请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/devices/activate [post]
func (c *DeviceController) ActivateDevice(ctx *gin.Context) {
	var req RegisterDeviceRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	if err := c.deviceService.ActivateDevice(req.SN, req.SecretKey); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "设备激活成功",
	})
}

// RegisterDevice 设备注册（设备端调用）
func (c *DeviceController) RegisterDevice(ctx *gin.Context) {
	var req RegisterDeviceRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
		})
		return
	}

	if err := c.deviceService.ActivateDevice(req.SN, req.SecretKey); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "设备注册成功",
	})
}

// DeviceHeartbeat 设备心跳（设备端调用）
func (c *DeviceController) DeviceHeartbeat(ctx *gin.Context) {
	var req DeviceHeartbeatRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
		})
		return
	}

	if err := c.deviceService.UpdateDeviceStatus(req.SN, req.Status, req.Battery, req.RSSI); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "心跳更新成功",
	})
}

// DeviceWebSocket 设备WebSocket连接
func (c *DeviceController) DeviceWebSocket(ctx *gin.Context) {
	deviceSN := ctx.Param("sn")
	
	conn, err := upgrader.Upgrade(ctx.Writer, ctx.Request, nil)
	if err != nil {
		return
	}
	defer conn.Close()

	// TODO: 实现WebSocket消息处理逻辑
	for {
		messageType, message, err := conn.ReadMessage()
		if err != nil {
			break
		}

		// 回显消息（示例）
		if err := conn.WriteMessage(messageType, message); err != nil {
			break
		}
	}
}

// MonitorWebSocket 监控WebSocket连接
func (c *DeviceController) MonitorWebSocket(ctx *gin.Context) {
	conn, err := upgrader.Upgrade(ctx.Writer, ctx.Request, nil)
	if err != nil {
		return
	}
	defer conn.Close()

	// TODO: 实现监控数据推送逻辑
	for {
		messageType, message, err := conn.ReadMessage()
		if err != nil {
			break
		}

		// 回显消息（示例）
		if err := conn.WriteMessage(messageType, message); err != nil {
			break
		}
	}
}
