package model

import (
	"time"
	"gorm.io/gorm"
)

// Template 模板模型
type Template struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	Name        string         `json:"name" gorm:"size:100;not null"`
	Type        string         `json:"type" gorm:"size:20;not null;comment:price,product,promotion"`
	Width       int            `json:"width" gorm:"not null;comment:模板宽度"`
	Height      int            `json:"height" gorm:"not null;comment:模板高度"`
	Background  string         `json:"background" gorm:"size:20;default:#FFFFFF;comment:背景色"`
	MerchantID  string         `json:"merchant_id" gorm:"size:32;index;not null"`
	IsDefault   bool           `json:"is_default" gorm:"default:false;comment:是否默认模板"`
	Status      int            `json:"status" gorm:"default:1;comment:1-正常,0-禁用"`
	Preview     string         `json:"preview" gorm:"size:255;comment:预览图"`
	Config      string         `json:"config" gorm:"type:json;comment:模板配置"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Merchant    *Merchant      `json:"merchant,omitempty" gorm:"foreignKey:MerchantID;references:ID"`
	Elements    []TemplateElement `json:"elements,omitempty" gorm:"foreignKey:TemplateID"`
	Devices     []Device       `json:"devices,omitempty" gorm:"foreignKey:TemplateID"`
}

// TemplateElement 模板元素模型
type TemplateElement struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	TemplateID  uint           `json:"template_id" gorm:"index;not null"`
	Type        string         `json:"type" gorm:"size:20;not null;comment:text,image,qrcode,barcode,price"`
	Name        string         `json:"name" gorm:"size:100;not null"`
	X           int            `json:"x" gorm:"not null;comment:X坐标"`
	Y           int            `json:"y" gorm:"not null;comment:Y坐标"`
	Width       int            `json:"width" gorm:"not null;comment:宽度"`
	Height      int            `json:"height" gorm:"not null;comment:高度"`
	FontSize    int            `json:"font_size" gorm:"default:12;comment:字体大小"`
	FontWeight  string         `json:"font_weight" gorm:"size:20;default:normal;comment:字体粗细"`
	Color       string         `json:"color" gorm:"size:20;default:#000000;comment:颜色"`
	Align       string         `json:"align" gorm:"size:20;default:left;comment:对齐方式"`
	DataField   string         `json:"data_field" gorm:"size:50;comment:数据字段"`
	DefaultValue string        `json:"default_value" gorm:"size:255;comment:默认值"`
	Format      string         `json:"format" gorm:"size:100;comment:格式化规则"`
	Sort        int            `json:"sort" gorm:"default:0;comment:排序"`
	Config      string         `json:"config" gorm:"type:json;comment:元素配置"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Template    *Template      `json:"template,omitempty" gorm:"foreignKey:TemplateID"`
}

// Product 商品模型
type Product struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	Name        string         `json:"name" gorm:"size:200;not null"`
	Code        string         `json:"code" gorm:"size:100;not null"`
	Barcode     string         `json:"barcode" gorm:"size:50;index"`
	CategoryID  uint           `json:"category_id" gorm:"index"`
	MerchantID  string         `json:"merchant_id" gorm:"size:32;index;not null"`
	Brand       string         `json:"brand" gorm:"size:100"`
	Model       string         `json:"model" gorm:"size:100"`
	Spec        string         `json:"spec" gorm:"size:200;comment:规格"`
	Unit        string         `json:"unit" gorm:"size:20;comment:单位"`
	Price       int            `json:"price" gorm:"not null;comment:价格(分)"`
	OriginalPrice int          `json:"original_price" gorm:"comment:原价(分)"`
	CostPrice   int            `json:"cost_price" gorm:"comment:成本价(分)"`
	Stock       int            `json:"stock" gorm:"default:0;comment:库存"`
	MinStock    int            `json:"min_stock" gorm:"default:0;comment:最低库存"`
	Image       string         `json:"image" gorm:"size:255;comment:商品图片"`
	Images      string         `json:"images" gorm:"type:json;comment:商品图片集"`
	Description string         `json:"description" gorm:"type:text;comment:商品描述"`
	Status      int            `json:"status" gorm:"default:1;comment:1-正常,0-下架"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Merchant    *Merchant      `json:"merchant,omitempty" gorm:"foreignKey:MerchantID;references:ID"`
	Category    *ProductCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	Devices     []Device       `json:"devices,omitempty" gorm:"foreignKey:ProductID"`
	PriceLogs   []ProductPriceLog `json:"price_logs,omitempty" gorm:"foreignKey:ProductID"`
}

// ProductCategory 商品分类模型
type ProductCategory struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	Name        string         `json:"name" gorm:"size:100;not null"`
	Code        string         `json:"code" gorm:"size:50;not null"`
	MerchantID  string         `json:"merchant_id" gorm:"size:32;index;not null"`
	ParentID    uint           `json:"parent_id" gorm:"default:0"`
	Sort        int            `json:"sort" gorm:"default:0"`
	Icon        string         `json:"icon" gorm:"size:255"`
	Description string         `json:"description" gorm:"size:255"`
	Status      int            `json:"status" gorm:"default:1;comment:1-正常,0-禁用"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Merchant    *Merchant      `json:"merchant,omitempty" gorm:"foreignKey:MerchantID;references:ID"`
	Products    []Product      `json:"products,omitempty" gorm:"foreignKey:CategoryID"`
	Children    []ProductCategory `json:"children,omitempty" gorm:"foreignKey:ParentID"`
}

// ProductPriceLog 商品价格变更日志
type ProductPriceLog struct {
	ID          uint      `json:"id" gorm:"primarykey"`
	ProductID   uint      `json:"product_id" gorm:"index;not null"`
	OldPrice    int       `json:"old_price" gorm:"comment:原价格(分)"`
	NewPrice    int       `json:"new_price" gorm:"comment:新价格(分)"`
	ChangeType  string    `json:"change_type" gorm:"size:20;comment:变更类型"`
	Reason      string    `json:"reason" gorm:"size:255;comment:变更原因"`
	OperatorID  uint      `json:"operator_id" gorm:"comment:操作人ID"`
	CreatedAt   time.Time `json:"created_at"`
	
	// 关联关系
	Product     *Product  `json:"product,omitempty" gorm:"foreignKey:ProductID"`
	Operator    *User     `json:"operator,omitempty" gorm:"foreignKey:OperatorID"`
}

// TableName 设置表名
func (Template) TableName() string {
	return "templates"
}

func (TemplateElement) TableName() string {
	return "template_elements"
}

func (Product) TableName() string {
	return "products"
}

func (ProductCategory) TableName() string {
	return "product_categories"
}

func (ProductPriceLog) TableName() string {
	return "product_price_logs"
}
