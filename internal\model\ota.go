package model

import (
	"time"
	"gorm.io/gorm"
)

// Firmware 固件模型
type Firmware struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	Version     string         `json:"version" gorm:"size:20;not null;comment:版本号"`
	Model       string         `json:"model" gorm:"size:50;not null;comment:设备型号"`
	FileName    string         `json:"file_name" gorm:"size:255;not null;comment:文件名"`
	FilePath    string         `json:"file_path" gorm:"size:500;not null;comment:文件路径"`
	FileSize    int64          `json:"file_size" gorm:"not null;comment:文件大小"`
	FileHash    string         `json:"file_hash" gorm:"size:64;not null;comment:文件哈希"`
	Description string         `json:"description" gorm:"type:text;comment:版本描述"`
	ChangeLog   string         `json:"change_log" gorm:"type:text;comment:更新日志"`
	IsForced    bool           `json:"is_forced" gorm:"default:false;comment:是否强制更新"`
	IsActive    bool           `json:"is_active" gorm:"default:true;comment:是否激活"`
	DownloadURL string         `json:"download_url" gorm:"size:500;comment:下载地址"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	UpdateTasks []OTATask      `json:"update_tasks,omitempty" gorm:"foreignKey:FirmwareID"`
}

// OTATask OTA升级任务模型
type OTATask struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	Name        string         `json:"name" gorm:"size:100;not null;comment:任务名称"`
	FirmwareID  uint           `json:"firmware_id" gorm:"index;not null"`
	MerchantID  string         `json:"merchant_id" gorm:"size:32;index;not null"`
	TargetType  string         `json:"target_type" gorm:"size:20;not null;comment:all,store,device,model"`
	TargetIDs   string         `json:"target_ids" gorm:"type:json;comment:目标设备IDs"`
	Status      int            `json:"status" gorm:"default:0;comment:0-待执行,1-执行中,2-已完成,3-已取消"`
	TotalCount  int            `json:"total_count" gorm:"default:0;comment:总设备数"`
	SuccessCount int           `json:"success_count" gorm:"default:0;comment:成功数"`
	FailedCount int            `json:"failed_count" gorm:"default:0;comment:失败数"`
	StartTime   *time.Time     `json:"start_time" gorm:"comment:开始时间"`
	EndTime     *time.Time     `json:"end_time" gorm:"comment:结束时间"`
	CreatedBy   uint           `json:"created_by" gorm:"comment:创建人"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Firmware    *Firmware      `json:"firmware,omitempty" gorm:"foreignKey:FirmwareID"`
	Merchant    *Merchant      `json:"merchant,omitempty" gorm:"foreignKey:MerchantID;references:ID"`
	Creator     *User          `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
	Records     []OTARecord    `json:"records,omitempty" gorm:"foreignKey:TaskID"`
}

// OTARecord OTA升级记录模型
type OTARecord struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	TaskID      uint           `json:"task_id" gorm:"index;not null"`
	DeviceID    uint           `json:"device_id" gorm:"index;not null"`
	FromVersion string         `json:"from_version" gorm:"size:20;comment:原版本"`
	ToVersion   string         `json:"to_version" gorm:"size:20;comment:目标版本"`
	Status      int            `json:"status" gorm:"default:0;comment:0-待升级,1-下载中,2-升级中,3-成功,4-失败"`
	Progress    int            `json:"progress" gorm:"default:0;comment:进度百分比"`
	ErrorMsg    string         `json:"error_msg" gorm:"type:text;comment:错误信息"`
	StartTime   *time.Time     `json:"start_time" gorm:"comment:开始时间"`
	EndTime     *time.Time     `json:"end_time" gorm:"comment:结束时间"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	
	// 关联关系
	Task        *OTATask       `json:"task,omitempty" gorm:"foreignKey:TaskID"`
	Device      *Device        `json:"device,omitempty" gorm:"foreignKey:DeviceID"`
}

// DiffPackage 差分包模型
type DiffPackage struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	Model       string         `json:"model" gorm:"size:50;not null;comment:设备型号"`
	FromVersion string         `json:"from_version" gorm:"size:20;not null;comment:源版本"`
	ToVersion   string         `json:"to_version" gorm:"size:20;not null;comment:目标版本"`
	FileName    string         `json:"file_name" gorm:"size:255;not null;comment:差分包文件名"`
	FilePath    string         `json:"file_path" gorm:"size:500;not null;comment:文件路径"`
	FileSize    int64          `json:"file_size" gorm:"not null;comment:文件大小"`
	FileHash    string         `json:"file_hash" gorm:"size:64;not null;comment:文件哈希"`
	Signature   string         `json:"signature" gorm:"size:128;not null;comment:数字签名"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
}

// DeviceUpdateLog 设备升级日志
type DeviceUpdateLog struct {
	ID          uint      `json:"id" gorm:"primarykey"`
	DeviceID    uint      `json:"device_id" gorm:"index;not null"`
	TaskID      uint      `json:"task_id" gorm:"index"`
	Type        string    `json:"type" gorm:"size:20;not null;comment:download,install,verify,rollback"`
	Message     string    `json:"message" gorm:"type:text;not null"`
	Level       string    `json:"level" gorm:"size:10;default:info;comment:info,warn,error"`
	Data        string    `json:"data" gorm:"type:json;comment:额外数据"`
	CreatedAt   time.Time `json:"created_at"`
	
	// 关联关系
	Device      *Device   `json:"device,omitempty" gorm:"foreignKey:DeviceID"`
	Task        *OTATask  `json:"task,omitempty" gorm:"foreignKey:TaskID"`
}

// TableName 设置表名
func (Firmware) TableName() string {
	return "firmwares"
}

func (OTATask) TableName() string {
	return "ota_tasks"
}

func (OTARecord) TableName() string {
	return "ota_records"
}

func (DiffPackage) TableName() string {
	return "diff_packages"
}

func (DeviceUpdateLog) TableName() string {
	return "device_update_logs"
}
