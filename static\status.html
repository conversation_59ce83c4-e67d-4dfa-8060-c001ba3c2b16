<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统状态 - 电子价签管理系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
        }
        
        .status-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .status-value {
            color: #666;
            font-size: 14px;
        }
        
        .api-test {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.danger {
            background: #dc3545;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 系统状态监控</h1>
            <p>电子价签管理系统运行状态</p>
        </div>
        
        <div class="status-grid" id="statusGrid">
            <div class="status-card">
                <div class="status-title">🔄 正在检查系统状态...</div>
            </div>
        </div>
        
        <div class="api-test">
            <h3>🧪 API接口测试</h3>
            <button class="btn" onclick="testAPI('/health', 'GET')">健康检查</button>
            <button class="btn" onclick="testAPI('/api/stats', 'GET')">统计数据</button>
            <button class="btn" onclick="testAPI('/api/devices', 'GET')">设备列表</button>
            <button class="btn" onclick="testLogin()">登录测试</button>
            <button class="btn" onclick="refreshStatus()">刷新状态</button>
            
            <div class="log" id="apiLog">
                <div>等待API测试...</div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';
        
        function log(message) {
            const logElement = document.getElementById('apiLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('apiLog').innerHTML = '';
        }
        
        async function checkSystemStatus() {
            const statusGrid = document.getElementById('statusGrid');
            const checks = [
                {
                    name: '🌐 后端服务',
                    test: async () => {
                        const response = await fetch(`${API_BASE}/health`);
                        const data = await response.json();
                        return {
                            status: response.ok,
                            message: data.msg || '服务正常',
                            details: `版本: ${data.data?.version || 'N/A'}`
                        };
                    }
                },
                {
                    name: '📊 API接口',
                    test: async () => {
                        const response = await fetch(`${API_BASE}/api/stats`);
                        const data = await response.json();
                        return {
                            status: response.ok && data.code === 200,
                            message: data.msg || 'API正常',
                            details: `设备数: ${data.data?.device_count || 0}`
                        };
                    }
                },
                {
                    name: '🔐 认证系统',
                    test: async () => {
                        const response = await fetch(`${API_BASE}/api/auth/login`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ username: 'admin', password: '123456' })
                        });
                        const data = await response.json();
                        return {
                            status: response.ok && data.code === 200,
                            message: data.msg || '认证正常',
                            details: `用户: ${data.data?.user?.real_name || 'N/A'}`
                        };
                    }
                },
                {
                    name: '📱 设备接口',
                    test: async () => {
                        const response = await fetch(`${API_BASE}/api/devices`);
                        const data = await response.json();
                        return {
                            status: response.ok && data.code === 200,
                            message: data.msg || '设备接口正常',
                            details: `设备数: ${data.data?.list?.length || 0}`
                        };
                    }
                }
            ];
            
            let html = '';
            
            for (const check of checks) {
                try {
                    const result = await check.test();
                    html += `
                        <div class="status-card ${result.status ? '' : 'error'}">
                            <div class="status-title">${check.name}</div>
                            <div class="status-value">
                                状态: ${result.status ? '✅ 正常' : '❌ 异常'}<br>
                                ${result.message}<br>
                                ${result.details}
                            </div>
                        </div>
                    `;
                } catch (error) {
                    html += `
                        <div class="status-card error">
                            <div class="status-title">${check.name}</div>
                            <div class="status-value">
                                状态: ❌ 连接失败<br>
                                错误: ${error.message}
                            </div>
                        </div>
                    `;
                }
            }
            
            statusGrid.innerHTML = html;
        }
        
        async function testAPI(endpoint, method = 'GET') {
            clearLog();
            log(`测试 ${method} ${endpoint}`);
            
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                log(`响应状态: ${response.status} ${response.statusText}`);
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (response.ok) {
                    log('✅ 测试成功');
                } else {
                    log('❌ 测试失败');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`);
            }
        }
        
        async function testLogin() {
            clearLog();
            log('测试登录接口...');
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: '123456'
                    })
                });
                
                const data = await response.json();
                
                log(`响应状态: ${response.status}`);
                log(`响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (data.code === 200) {
                    log('✅ 登录成功');
                } else {
                    log('❌ 登录失败');
                }
            } catch (error) {
                log(`❌ 登录请求失败: ${error.message}`);
            }
        }
        
        function refreshStatus() {
            checkSystemStatus();
        }
        
        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
            
            // 每30秒自动刷新状态
            setInterval(checkSystemStatus, 30000);
        });
    </script>
</body>
</html>
