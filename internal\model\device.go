package model

import (
	"time"
	"gorm.io/gorm"
)

// Device 设备模型
type Device struct {
	ID           uint           `json:"id" gorm:"primarykey"`
	SN           string         `json:"sn" gorm:"uniqueIndex;size:64;not null;comment:设备序列号"`
	Name         string         `json:"name" gorm:"size:100;not null"`
	Model        string         `json:"model" gorm:"size:50;not null;comment:设备型号"`
	Version      string         `json:"version" gorm:"size:20;comment:固件版本"`
	MerchantID   string         `json:"merchant_id" gorm:"size:32;index;not null"`
	StoreID      uint           `json:"store_id" gorm:"index"`
	LocationID   uint           `json:"location_id" gorm:"index"`
	TemplateID   uint           `json:"template_id" gorm:"index"`
	ProductID    uint           `json:"product_id" gorm:"index"`
	Status       int            `json:"status" gorm:"default:0;comment:0-离线,1-在线,2-故障"`
	Battery      int            `json:"battery" gorm:"default:0;comment:电池电量百分比"`
	RSSI         int            `json:"rssi" gorm:"default:0;comment:信号强度"`
	LastSeen     *time.Time     `json:"last_seen" gorm:"comment:最后在线时间"`
	SecretKey    string         `json:"-" gorm:"size:64;comment:设备密钥"`
	IsActivated  bool           `json:"is_activated" gorm:"default:false;comment:是否已激活"`
	ActivatedAt  *time.Time     `json:"activated_at" gorm:"comment:激活时间"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Merchant     *Merchant      `json:"merchant,omitempty" gorm:"foreignKey:MerchantID;references:ID"`
	Store        *Store         `json:"store,omitempty" gorm:"foreignKey:StoreID"`
	Location     *Location      `json:"location,omitempty" gorm:"foreignKey:LocationID"`
	Template     *Template      `json:"template,omitempty" gorm:"foreignKey:TemplateID"`
	Product      *Product       `json:"product,omitempty" gorm:"foreignKey:ProductID"`
	StatusLogs   []DeviceLog    `json:"status_logs,omitempty" gorm:"foreignKey:DeviceID"`
}

// Store 门店模型
type Store struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	Name        string         `json:"name" gorm:"size:100;not null"`
	Code        string         `json:"code" gorm:"size:50;not null"`
	MerchantID  string         `json:"merchant_id" gorm:"size:32;index;not null"`
	Address     string         `json:"address" gorm:"size:255"`
	Phone       string         `json:"phone" gorm:"size:20"`
	Manager     string         `json:"manager" gorm:"size:50"`
	Status      int            `json:"status" gorm:"default:1;comment:1-正常,0-禁用"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Merchant    *Merchant      `json:"merchant,omitempty" gorm:"foreignKey:MerchantID;references:ID"`
	Locations   []Location     `json:"locations,omitempty" gorm:"foreignKey:StoreID"`
	Devices     []Device       `json:"devices,omitempty" gorm:"foreignKey:StoreID"`
}

// Location 位置模型
type Location struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	Name        string         `json:"name" gorm:"size:100;not null"`
	Code        string         `json:"code" gorm:"size:50;not null"`
	StoreID     uint           `json:"store_id" gorm:"index;not null"`
	Floor       string         `json:"floor" gorm:"size:20"`
	Area        string         `json:"area" gorm:"size:50"`
	Description string         `json:"description" gorm:"size:255"`
	Status      int            `json:"status" gorm:"default:1;comment:1-正常,0-禁用"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Store       *Store         `json:"store,omitempty" gorm:"foreignKey:StoreID"`
	Devices     []Device       `json:"devices,omitempty" gorm:"foreignKey:LocationID"`
}

// DeviceLog 设备日志模型
type DeviceLog struct {
	ID        uint      `json:"id" gorm:"primarykey"`
	DeviceID  uint      `json:"device_id" gorm:"index;not null"`
	Type      string    `json:"type" gorm:"size:20;not null;comment:status,battery,error,update"`
	Content   string    `json:"content" gorm:"type:text"`
	Data      string    `json:"data" gorm:"type:json;comment:额外数据"`
	CreatedAt time.Time `json:"created_at"`
	
	// 关联关系
	Device    *Device   `json:"device,omitempty" gorm:"foreignKey:DeviceID"`
}

// DeviceCommand 设备命令模型
type DeviceCommand struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	DeviceID    uint           `json:"device_id" gorm:"index;not null"`
	Type        string         `json:"type" gorm:"size:20;not null;comment:update_display,ota_update,reboot"`
	Command     string         `json:"command" gorm:"type:text;not null"`
	Params      string         `json:"params" gorm:"type:json"`
	Status      int            `json:"status" gorm:"default:0;comment:0-待执行,1-执行中,2-成功,3-失败"`
	Result      string         `json:"result" gorm:"type:text"`
	ExecutedAt  *time.Time     `json:"executed_at"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Device      *Device        `json:"device,omitempty" gorm:"foreignKey:DeviceID"`
}

// TableName 设置表名
func (Device) TableName() string {
	return "devices"
}

func (Store) TableName() string {
	return "stores"
}

func (Location) TableName() string {
	return "locations"
}

func (DeviceLog) TableName() string {
	return "device_logs"
}

func (DeviceCommand) TableName() string {
	return "device_commands"
}
