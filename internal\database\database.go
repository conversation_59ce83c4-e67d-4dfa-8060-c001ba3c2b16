package database

import (
	"context"
	"electronic-price-tag/internal/config"
	"electronic-price-tag/internal/model"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Init 初始化数据库连接
func Init(cfg config.DatabaseConfig) (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		cfg.Username,
		cfg.Password,
		cfg.Host,
		cfg.Port,
		cfg.Database,
		cfg.Charset,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 设置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get database instance: %w", err)
	}

	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// 自动迁移
	if err := autoMigrate(db); err != nil {
		return nil, fmt.Errorf("failed to auto migrate: %w", err)
	}

	// 初始化基础数据
	if err := initBaseData(db); err != nil {
		return nil, fmt.Errorf("failed to init base data: %w", err)
	}

	return db, nil
}

// InitRedis 初始化Redis连接
func InitRedis(cfg config.RedisConfig) (*redis.Client, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password: cfg.Password,
		DB:       cfg.DB,
	})

	// 测试连接
	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to connect to redis: %w", err)
	}

	return rdb, nil
}

// autoMigrate 自动迁移数据库表
func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		// 用户相关
		&model.User{},
		&model.Role{},
		&model.Permission{},
		&model.Merchant{},
		&model.UserRole{},
		&model.RolePermission{},
		
		// 设备相关
		&model.Device{},
		&model.Store{},
		&model.Location{},
		&model.DeviceLog{},
		&model.DeviceCommand{},
		
		// 模板和商品相关
		&model.Template{},
		&model.TemplateElement{},
		&model.Product{},
		&model.ProductCategory{},
		&model.ProductPriceLog{},
		
		// OTA相关
		&model.Firmware{},
		&model.OTATask{},
		&model.OTARecord{},
		&model.DiffPackage{},
		&model.DeviceUpdateLog{},
	)
}

// initBaseData 初始化基础数据
func initBaseData(db *gorm.DB) error {
	// 创建默认商户
	var merchant model.Merchant
	if err := db.Where("code = ?", "default").First(&merchant).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			merchant = model.Merchant{
				ID:     "M000001",
				Name:   "默认商户",
				Code:   "default",
				Status: 1,
			}
			if err := db.Create(&merchant).Error; err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// 创建默认权限
	permissions := []model.Permission{
		{Code: "system:user:list", Name: "用户列表", Type: "api", Path: "/api/users", Method: "GET"},
		{Code: "system:user:create", Name: "创建用户", Type: "api", Path: "/api/users", Method: "POST"},
		{Code: "system:user:update", Name: "更新用户", Type: "api", Path: "/api/users/*", Method: "PUT"},
		{Code: "system:user:delete", Name: "删除用户", Type: "api", Path: "/api/users/*", Method: "DELETE"},
		{Code: "system:role:list", Name: "角色列表", Type: "api", Path: "/api/roles", Method: "GET"},
		{Code: "system:role:create", Name: "创建角色", Type: "api", Path: "/api/roles", Method: "POST"},
		{Code: "system:role:update", Name: "更新角色", Type: "api", Path: "/api/roles/*", Method: "PUT"},
		{Code: "system:role:delete", Name: "删除角色", Type: "api", Path: "/api/roles/*", Method: "DELETE"},
		{Code: "device:list", Name: "设备列表", Type: "api", Path: "/api/devices", Method: "GET"},
		{Code: "device:create", Name: "创建设备", Type: "api", Path: "/api/devices", Method: "POST"},
		{Code: "device:update", Name: "更新设备", Type: "api", Path: "/api/devices/*", Method: "PUT"},
		{Code: "device:delete", Name: "删除设备", Type: "api", Path: "/api/devices/*", Method: "DELETE"},
		{Code: "device:control", Name: "设备控制", Type: "api", Path: "/api/devices/*/control", Method: "POST"},
		{Code: "template:list", Name: "模板列表", Type: "api", Path: "/api/templates", Method: "GET"},
		{Code: "template:create", Name: "创建模板", Type: "api", Path: "/api/templates", Method: "POST"},
		{Code: "template:update", Name: "更新模板", Type: "api", Path: "/api/templates/*", Method: "PUT"},
		{Code: "template:delete", Name: "删除模板", Type: "api", Path: "/api/templates/*", Method: "DELETE"},
		{Code: "product:list", Name: "商品列表", Type: "api", Path: "/api/products", Method: "GET"},
		{Code: "product:create", Name: "创建商品", Type: "api", Path: "/api/products", Method: "POST"},
		{Code: "product:update", Name: "更新商品", Type: "api", Path: "/api/products/*", Method: "PUT"},
		{Code: "product:delete", Name: "删除商品", Type: "api", Path: "/api/products/*", Method: "DELETE"},
		{Code: "ota:list", Name: "OTA列表", Type: "api", Path: "/api/ota/tasks", Method: "GET"},
		{Code: "ota:create", Name: "创建OTA任务", Type: "api", Path: "/api/ota/tasks", Method: "POST"},
		{Code: "ota:update", Name: "更新OTA任务", Type: "api", Path: "/api/ota/tasks/*", Method: "PUT"},
		{Code: "ota:delete", Name: "删除OTA任务", Type: "api", Path: "/api/ota/tasks/*", Method: "DELETE"},
	}

	for _, perm := range permissions {
		var existPerm model.Permission
		if err := db.Where("code = ?", perm.Code).First(&existPerm).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := db.Create(&perm).Error; err != nil {
					return err
				}
			} else {
				return err
			}
		}
	}

	// 创建默认角色
	var adminRole model.Role
	if err := db.Where("code = ?", "admin").First(&adminRole).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			adminRole = model.Role{
				Name:        "超级管理员",
				Code:        "admin",
				Description: "系统超级管理员",
				Status:      1,
			}
			if err := db.Create(&adminRole).Error; err != nil {
				return err
			}

			// 给管理员角色分配所有权限
			var allPerms []model.Permission
			db.Find(&allPerms)
			if err := db.Model(&adminRole).Association("Permissions").Append(allPerms); err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// 创建默认管理员用户
	var adminUser model.User
	if err := db.Where("username = ?", "admin").First(&adminUser).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			adminUser = model.User{
				Username:   "admin",
				Password:   "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZFzDO6OzO6T7.jbNjpjSuADAa", // 123456
				Email:      "<EMAIL>",
				RealName:   "系统管理员",
				Status:     1,
				MerchantID: merchant.ID,
			}
			if err := db.Create(&adminUser).Error; err != nil {
				return err
			}

			// 给用户分配管理员角色
			if err := db.Model(&adminUser).Association("Roles").Append(&adminRole); err != nil {
				return err
			}
		} else {
			return err
		}
	}

	return nil
}
