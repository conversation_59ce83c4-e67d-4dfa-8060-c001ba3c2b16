package service

import (
	"electronic-price-tag/internal/model"
	"electronic-price-tag/pkg/logger"
	"electronic-price-tag/pkg/mqtt"
	"encoding/json"
	"strings"
	"time"

	"gorm.io/gorm"
)

type MQTTService interface {
	HandleDeviceMessage(topic string, payload []byte) error
	PublishToDevice(deviceSN string, message interface{}) error
	PublishBroadcast(message interface{}) error
}

type mqttService struct {
	mqttClient *mqtt.Client
	db         *gorm.DB
}

// DeviceStatusMessage 设备状态消息
type DeviceStatusMessage struct {
	SN       string    `json:"sn"`
	Status   int       `json:"status"`
	Battery  int       `json:"battery"`
	RSSI     int       `json:"rssi"`
	Version  string    `json:"version"`
	LastSeen time.Time `json:"last_seen"`
}

// DeviceHeartbeatMessage 设备心跳消息
type DeviceHeartbeatMessage struct {
	SN        string    `json:"sn"`
	Timestamp time.Time `json:"timestamp"`
	Battery   int       `json:"battery"`
	RSSI      int       `json:"rssi"`
}

// DeviceResponseMessage 设备响应消息
type DeviceResponseMessage struct {
	SN        string      `json:"sn"`
	CommandID string      `json:"command_id"`
	Status    int         `json:"status"`
	Result    interface{} `json:"result"`
	Error     string      `json:"error"`
}

// DeviceLogMessage 设备日志消息
type DeviceLogMessage struct {
	SN        string    `json:"sn"`
	Level     string    `json:"level"`
	Message   string    `json:"message"`
	Timestamp time.Time `json:"timestamp"`
	Data      string    `json:"data"`
}

// DeviceOTAMessage 设备OTA消息
type DeviceOTAMessage struct {
	SN       string `json:"sn"`
	TaskID   uint   `json:"task_id"`
	RecordID uint   `json:"record_id"`
	Status   int    `json:"status"`
	Progress int    `json:"progress"`
	Error    string `json:"error"`
}

func NewMQTTService(mqttClient *mqtt.Client, db *gorm.DB) MQTTService {
	service := &mqttService{
		mqttClient: mqttClient,
		db:         db,
	}

	// 设置消息处理器
	service.setupMessageHandlers()

	return service
}

// setupMessageHandlers 设置消息处理器
func (s *mqttService) setupMessageHandlers() {
	// 重新订阅主题并设置处理器
	topics := map[string]func(string, []byte) error{
		"device/+/status":    s.handleDeviceStatus,
		"device/+/heartbeat": s.handleDeviceHeartbeat,
		"device/+/response":  s.handleDeviceResponse,
		"device/+/log":       s.handleDeviceLog,
		"device/+/ota":       s.handleDeviceOTA,
	}

	for topic, handler := range topics {
		s.mqttClient.Subscribe(topic, 1, func(topic string, payload []byte) {
			if err := handler(topic, payload); err != nil {
				logger.Error("Failed to handle MQTT message for topic %s: %v", topic, err)
			}
		})
	}
}

// HandleDeviceMessage 处理设备消息
func (s *mqttService) HandleDeviceMessage(topic string, payload []byte) error {
	logger.Debug("Received MQTT message: topic=%s, payload=%s", topic, string(payload))

	// 根据主题路由到不同的处理器
	if strings.Contains(topic, "/status") {
		return s.handleDeviceStatus(topic, payload)
	} else if strings.Contains(topic, "/heartbeat") {
		return s.handleDeviceHeartbeat(topic, payload)
	} else if strings.Contains(topic, "/response") {
		return s.handleDeviceResponse(topic, payload)
	} else if strings.Contains(topic, "/log") {
		return s.handleDeviceLog(topic, payload)
	} else if strings.Contains(topic, "/ota") {
		return s.handleDeviceOTA(topic, payload)
	}

	return nil
}

// handleDeviceStatus 处理设备状态消息
func (s *mqttService) handleDeviceStatus(topic string, payload []byte) error {
	var statusMsg DeviceStatusMessage
	if err := json.Unmarshal(payload, &statusMsg); err != nil {
		logger.Error("Failed to unmarshal device status message: %v", err)
		return err
	}

	// 更新设备状态
	var device model.Device
	if err := s.db.Where("sn = ?", statusMsg.SN).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			logger.Warn("Device not found: %s", statusMsg.SN)
			return nil
		}
		return err
	}

	// 更新设备信息
	now := time.Now()
	updates := map[string]interface{}{
		"status":    statusMsg.Status,
		"battery":   statusMsg.Battery,
		"rssi":      statusMsg.RSSI,
		"last_seen": &now,
	}

	if statusMsg.Version != "" {
		updates["version"] = statusMsg.Version
	}

	if err := s.db.Model(&device).Updates(updates).Error; err != nil {
		logger.Error("Failed to update device status: %v", err)
		return err
	}

	// 记录状态日志
	log := model.DeviceLog{
		DeviceID: device.ID,
		Type:     "status",
		Content:  "设备状态更新",
		Data:     string(payload),
	}
	s.db.Create(&log)

	logger.Debug("Updated device status: SN=%s, Status=%d, Battery=%d, RSSI=%d", 
		statusMsg.SN, statusMsg.Status, statusMsg.Battery, statusMsg.RSSI)

	return nil
}

// handleDeviceHeartbeat 处理设备心跳消息
func (s *mqttService) handleDeviceHeartbeat(topic string, payload []byte) error {
	var heartbeatMsg DeviceHeartbeatMessage
	if err := json.Unmarshal(payload, &heartbeatMsg); err != nil {
		logger.Error("Failed to unmarshal device heartbeat message: %v", err)
		return err
	}

	// 更新设备最后在线时间
	now := time.Now()
	updates := map[string]interface{}{
		"last_seen": &now,
		"status":    1, // 在线状态
	}

	if heartbeatMsg.Battery > 0 {
		updates["battery"] = heartbeatMsg.Battery
	}
	if heartbeatMsg.RSSI != 0 {
		updates["rssi"] = heartbeatMsg.RSSI
	}

	if err := s.db.Model(&model.Device{}).Where("sn = ?", heartbeatMsg.SN).Updates(updates).Error; err != nil {
		logger.Error("Failed to update device heartbeat: %v", err)
		return err
	}

	logger.Debug("Received device heartbeat: SN=%s", heartbeatMsg.SN)

	return nil
}

// handleDeviceResponse 处理设备响应消息
func (s *mqttService) handleDeviceResponse(topic string, payload []byte) error {
	var responseMsg DeviceResponseMessage
	if err := json.Unmarshal(payload, &responseMsg); err != nil {
		logger.Error("Failed to unmarshal device response message: %v", err)
		return err
	}

	// 查找对应的设备命令
	var device model.Device
	if err := s.db.Where("sn = ?", responseMsg.SN).First(&device).Error; err != nil {
		logger.Warn("Device not found: %s", responseMsg.SN)
		return nil
	}

	// 更新命令状态
	var command model.DeviceCommand
	if err := s.db.Where("device_id = ? AND status = 1", device.ID).
		Order("created_at DESC").First(&command).Error; err != nil {
		logger.Warn("No pending command found for device: %s", responseMsg.SN)
		return nil
	}

	// 更新命令结果
	now := time.Now()
	updates := map[string]interface{}{
		"status":      responseMsg.Status,
		"executed_at": &now,
	}

	if responseMsg.Result != nil {
		resultJSON, _ := json.Marshal(responseMsg.Result)
		updates["result"] = string(resultJSON)
	}

	if responseMsg.Error != "" {
		updates["result"] = responseMsg.Error
	}

	if err := s.db.Model(&command).Updates(updates).Error; err != nil {
		logger.Error("Failed to update device command: %v", err)
		return err
	}

	// 记录响应日志
	log := model.DeviceLog{
		DeviceID: device.ID,
		Type:     "response",
		Content:  "设备命令响应",
		Data:     string(payload),
	}
	s.db.Create(&log)

	logger.Debug("Received device response: SN=%s, CommandID=%s, Status=%d", 
		responseMsg.SN, responseMsg.CommandID, responseMsg.Status)

	return nil
}

// handleDeviceLog 处理设备日志消息
func (s *mqttService) handleDeviceLog(topic string, payload []byte) error {
	var logMsg DeviceLogMessage
	if err := json.Unmarshal(payload, &logMsg); err != nil {
		logger.Error("Failed to unmarshal device log message: %v", err)
		return err
	}

	// 查找设备
	var device model.Device
	if err := s.db.Where("sn = ?", logMsg.SN).First(&device).Error; err != nil {
		logger.Warn("Device not found: %s", logMsg.SN)
		return nil
	}

	// 保存设备日志
	log := model.DeviceLog{
		DeviceID: device.ID,
		Type:     "log",
		Content:  logMsg.Message,
		Data:     logMsg.Data,
	}
	s.db.Create(&log)

	logger.Debug("Received device log: SN=%s, Level=%s, Message=%s", 
		logMsg.SN, logMsg.Level, logMsg.Message)

	return nil
}

// handleDeviceOTA 处理设备OTA消息
func (s *mqttService) handleDeviceOTA(topic string, payload []byte) error {
	var otaMsg DeviceOTAMessage
	if err := json.Unmarshal(payload, &otaMsg); err != nil {
		logger.Error("Failed to unmarshal device OTA message: %v", err)
		return err
	}

	// 查找设备
	var device model.Device
	if err := s.db.Where("sn = ?", otaMsg.SN).First(&device).Error; err != nil {
		logger.Warn("Device not found: %s", otaMsg.SN)
		return nil
	}

	// 更新OTA记录
	if otaMsg.RecordID > 0 {
		updates := map[string]interface{}{
			"status":   otaMsg.Status,
			"progress": otaMsg.Progress,
		}

		if otaMsg.Error != "" {
			updates["error_msg"] = otaMsg.Error
		}

		if otaMsg.Status == 3 || otaMsg.Status == 4 { // 成功或失败
			now := time.Now()
			updates["end_time"] = &now
		}

		if err := s.db.Model(&model.OTARecord{}).Where("id = ?", otaMsg.RecordID).Updates(updates).Error; err != nil {
			logger.Error("Failed to update OTA record: %v", err)
			return err
		}
	}

	// 记录OTA日志
	log := model.DeviceUpdateLog{
		DeviceID: device.ID,
		TaskID:   otaMsg.TaskID,
		Type:     "ota",
		Message:  "OTA升级进度更新",
		Data:     string(payload),
	}
	s.db.Create(&log)

	logger.Debug("Received device OTA message: SN=%s, TaskID=%d, Status=%d, Progress=%d", 
		otaMsg.SN, otaMsg.TaskID, otaMsg.Status, otaMsg.Progress)

	return nil
}

// PublishToDevice 向指定设备发布消息
func (s *mqttService) PublishToDevice(deviceSN string, message interface{}) error {
	topic := "device/" + deviceSN + "/command"
	
	payload, err := json.Marshal(message)
	if err != nil {
		return err
	}

	return s.mqttClient.Publish(topic, 1, false, payload)
}

// PublishBroadcast 广播消息
func (s *mqttService) PublishBroadcast(message interface{}) error {
	topic := "broadcast/all"
	
	payload, err := json.Marshal(message)
	if err != nil {
		return err
	}

	return s.mqttClient.Publish(topic, 1, false, payload)
}
