import request from '@/utils/request'

// 获取设备列表
export function getDevices(params) {
  return request({
    url: '/devices',
    method: 'get',
    params
  })
}

// 获取设备详情
export function getDevice(id) {
  return request({
    url: `/devices/${id}`,
    method: 'get'
  })
}

// 创建设备
export function createDevice(data) {
  return request({
    url: '/devices',
    method: 'post',
    data
  })
}

// 更新设备
export function updateDevice(id, data) {
  return request({
    url: `/devices/${id}`,
    method: 'put',
    data
  })
}

// 删除设备
export function deleteDevice(id) {
  return request({
    url: `/devices/${id}`,
    method: 'delete'
  })
}

// 发送设备命令
export function sendDeviceCommand(id, data) {
  return request({
    url: `/devices/${id}/commands`,
    method: 'post',
    data
  })
}

// 获取设备命令列表
export function getDeviceCommands(id, params) {
  return request({
    url: `/devices/${id}/commands`,
    method: 'get',
    params
  })
}

// 获取设备日志
export function getDeviceLogs(id, params) {
  return request({
    url: `/devices/${id}/logs`,
    method: 'get',
    params
  })
}

// 批量更新设备显示
export function batchUpdateDisplay(data) {
  return request({
    url: '/devices/batch-update',
    method: 'post',
    data
  })
}

// 激活设备
export function activateDevice(data) {
  return request({
    url: '/devices/activate',
    method: 'post',
    data
  })
}

// 获取设备统计
export function getDeviceStats() {
  return request({
    url: '/devices/stats',
    method: 'get'
  })
}
