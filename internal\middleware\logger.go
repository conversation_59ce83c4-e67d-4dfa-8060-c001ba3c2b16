package middleware

import (
	"bytes"
	"electronic-price-tag/pkg/logger"
	"io"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// responseWriter 自定义响应写入器
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// Logger 日志中间件
func Logger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// 记录请求日志
		logger.WithFields(logrus.Fields{
			"status_code":  param.StatusCode,
			"latency":      param.Latency,
			"client_ip":    param.ClientIP,
			"method":       param.Method,
			"path":         param.Path,
			"user_agent":   param.Request.UserAgent(),
			"error":        param.ErrorMessage,
		}).Info("HTTP Request")

		return ""
	})
}

// RequestLogger 详细请求日志中间件
func RequestLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录请求开始时间
		start := time.Now()

		// 读取请求体
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 创建自定义响应写入器
		w := &responseWriter{
			ResponseWriter: c.Writer,
			body:           bytes.NewBufferString(""),
		}
		c.Writer = w

		// 处理请求
		c.Next()

		// 计算处理时间
		latency := time.Since(start)

		// 获取用户信息
		userID, _ := c.Get("user_id")
		username, _ := c.Get("username")

		// 记录详细日志
		fields := logrus.Fields{
			"timestamp":    start.Format("2006-01-02 15:04:05"),
			"latency":      latency,
			"status_code":  c.Writer.Status(),
			"method":       c.Request.Method,
			"path":         c.Request.URL.Path,
			"query":        c.Request.URL.RawQuery,
			"client_ip":    c.ClientIP(),
			"user_agent":   c.Request.UserAgent(),
			"request_size": c.Request.ContentLength,
			"response_size": w.body.Len(),
		}

		// 添加用户信息
		if userID != nil {
			fields["user_id"] = userID
		}
		if username != nil {
			fields["username"] = username
		}

		// 记录请求体（仅对POST/PUT/PATCH请求）
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			if len(requestBody) > 0 && len(requestBody) < 1024 { // 限制大小
				fields["request_body"] = string(requestBody)
			}
		}

		// 记录响应体（仅对错误响应）
		if c.Writer.Status() >= 400 {
			if w.body.Len() > 0 && w.body.Len() < 1024 { // 限制大小
				fields["response_body"] = w.body.String()
			}
		}

		// 根据状态码选择日志级别
		if c.Writer.Status() >= 500 {
			logger.WithFields(fields).Error("HTTP Request")
		} else if c.Writer.Status() >= 400 {
			logger.WithFields(fields).Warn("HTTP Request")
		} else {
			logger.WithFields(fields).Info("HTTP Request")
		}
	}
}

// ErrorLogger 错误日志中间件
func ErrorLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 记录错误
		if len(c.Errors) > 0 {
			for _, err := range c.Errors {
				logger.WithFields(logrus.Fields{
					"method": c.Request.Method,
					"path":   c.Request.URL.Path,
					"ip":     c.ClientIP(),
					"error":  err.Error(),
				}).Error("Request Error")
			}
		}
	}
}
