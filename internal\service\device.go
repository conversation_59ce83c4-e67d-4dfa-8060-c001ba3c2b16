package service

import (
	"context"
	"electronic-price-tag/internal/model"
	"electronic-price-tag/pkg/logger"
	"electronic-price-tag/pkg/mqtt"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type DeviceService interface {
	GetDevices(merchantID string, page, pageSize int, keyword string) (*DeviceListResponse, error)
	GetDeviceByID(id uint) (*model.Device, error)
	GetDeviceBySN(sn string) (*model.Device, error)
	CreateDevice(req *CreateDeviceRequest) (*model.Device, error)
	UpdateDevice(id uint, req *UpdateDeviceRequest) error
	DeleteDevice(id uint) error
	ActivateDevice(sn, secretKey string) error
	UpdateDeviceStatus(sn string, status int, battery, rssi int) error
	SendCommand(deviceID uint, command *DeviceCommand) error
	GetDeviceCommands(deviceID uint, page, pageSize int) (*DeviceCommandListResponse, error)
	GetDeviceLogs(deviceID uint, page, pageSize int, logType string) (*DeviceLogListResponse, error)
	BatchUpdateDisplay(deviceIDs []uint, templateID uint) error
}

type deviceService struct {
	db         *gorm.DB
	rdb        *redis.Client
	mqttClient *mqtt.Client
}

type DeviceListResponse struct {
	Total int            `json:"total"`
	List  []model.Device `json:"list"`
}

type DeviceCommandListResponse struct {
	Total int                   `json:"total"`
	List  []model.DeviceCommand `json:"list"`
}

type DeviceLogListResponse struct {
	Total int               `json:"total"`
	List  []model.DeviceLog `json:"list"`
}

type CreateDeviceRequest struct {
	SN         string `json:"sn" binding:"required"`
	Name       string `json:"name" binding:"required"`
	Model      string `json:"model" binding:"required"`
	MerchantID string `json:"merchant_id" binding:"required"`
	StoreID    uint   `json:"store_id"`
	LocationID uint   `json:"location_id"`
	TemplateID uint   `json:"template_id"`
	ProductID  uint   `json:"product_id"`
}

type UpdateDeviceRequest struct {
	Name       string `json:"name"`
	StoreID    *uint  `json:"store_id"`
	LocationID *uint  `json:"location_id"`
	TemplateID *uint  `json:"template_id"`
	ProductID  *uint  `json:"product_id"`
	Status     *int   `json:"status"`
}

type DeviceCommand struct {
	Type    string      `json:"type"`
	Command string      `json:"command"`
	Params  interface{} `json:"params"`
}

func NewDeviceService(db *gorm.DB, rdb *redis.Client, mqttClient *mqtt.Client) DeviceService {
	return &deviceService{
		db:         db,
		rdb:        rdb,
		mqttClient: mqttClient,
	}
}

// GetDevices 获取设备列表
func (s *deviceService) GetDevices(merchantID string, page, pageSize int, keyword string) (*DeviceListResponse, error) {
	var devices []model.Device
	var total int64

	query := s.db.Model(&model.Device{}).
		Preload("Store").
		Preload("Location").
		Preload("Template").
		Preload("Product")

	// 商户过滤
	if merchantID != "" {
		query = query.Where("merchant_id = ?", merchantID)
	}

	// 关键词搜索
	if keyword != "" {
		query = query.Where("sn LIKE ? OR name LIKE ?",
			fmt.Sprintf("%%%s%%", keyword),
			fmt.Sprintf("%%%s%%", keyword))
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count devices: %v", err)
		return nil, errors.New("获取设备总数失败")
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Find(&devices).Error; err != nil {
		logger.Error("Failed to get devices: %v", err)
		return nil, errors.New("获取设备列表失败")
	}

	return &DeviceListResponse{
		Total: int(total),
		List:  devices,
	}, nil
}

// GetDeviceByID 根据ID获取设备
func (s *deviceService) GetDeviceByID(id uint) (*model.Device, error) {
	var device model.Device
	if err := s.db.Preload("Store").Preload("Location").Preload("Template").Preload("Product").
		Where("id = ?", id).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("设备不存在")
		}
		logger.Error("Failed to get device by id: %v", err)
		return nil, errors.New("获取设备信息失败")
	}

	return &device, nil
}

// GetDeviceBySN 根据SN获取设备
func (s *deviceService) GetDeviceBySN(sn string) (*model.Device, error) {
	var device model.Device
	if err := s.db.Preload("Store").Preload("Location").Preload("Template").Preload("Product").
		Where("sn = ?", sn).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("设备不存在")
		}
		logger.Error("Failed to get device by sn: %v", err)
		return nil, errors.New("获取设备信息失败")
	}

	return &device, nil
}

// CreateDevice 创建设备
func (s *deviceService) CreateDevice(req *CreateDeviceRequest) (*model.Device, error) {
	// 检查SN是否已存在
	var existDevice model.Device
	if err := s.db.Where("sn = ?", req.SN).First(&existDevice).Error; err == nil {
		return nil, errors.New("设备SN已存在")
	}

	// 创建设备
	device := model.Device{
		SN:         req.SN,
		Name:       req.Name,
		Model:      req.Model,
		MerchantID: req.MerchantID,
		StoreID:    req.StoreID,
		LocationID: req.LocationID,
		TemplateID: req.TemplateID,
		ProductID:  req.ProductID,
		Status:     0, // 默认离线状态
	}

	if err := s.db.Create(&device).Error; err != nil {
		logger.Error("Failed to create device: %v", err)
		return nil, errors.New("创建设备失败")
	}

	// 重新查询设备信息（包含关联数据）
	if err := s.db.Preload("Store").Preload("Location").Preload("Template").Preload("Product").
		Where("id = ?", device.ID).First(&device).Error; err != nil {
		logger.Error("Failed to reload device: %v", err)
	}

	return &device, nil
}

// UpdateDevice 更新设备
func (s *deviceService) UpdateDevice(id uint, req *UpdateDeviceRequest) error {
	// 检查设备是否存在
	var device model.Device
	if err := s.db.Where("id = ?", id).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("设备不存在")
		}
		return errors.New("查询设备失败")
	}

	// 更新设备信息
	updates := map[string]interface{}{}
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.StoreID != nil {
		updates["store_id"] = *req.StoreID
	}
	if req.LocationID != nil {
		updates["location_id"] = *req.LocationID
	}
	if req.TemplateID != nil {
		updates["template_id"] = *req.TemplateID
	}
	if req.ProductID != nil {
		updates["product_id"] = *req.ProductID
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	if len(updates) > 0 {
		if err := s.db.Model(&device).Updates(updates).Error; err != nil {
			logger.Error("Failed to update device: %v", err)
			return errors.New("更新设备失败")
		}
	}

	return nil
}

// DeleteDevice 删除设备
func (s *deviceService) DeleteDevice(id uint) error {
	// 检查设备是否存在
	var device model.Device
	if err := s.db.Where("id = ?", id).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("设备不存在")
		}
		return errors.New("查询设备失败")
	}

	// 软删除设备
	if err := s.db.Delete(&device).Error; err != nil {
		logger.Error("Failed to delete device: %v", err)
		return errors.New("删除设备失败")
	}

	return nil
}

// ActivateDevice 激活设备
func (s *deviceService) ActivateDevice(sn, secretKey string) error {
	var device model.Device
	if err := s.db.Where("sn = ?", sn).First(&device).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return errors.New("设备不存在")
		}
		return errors.New("查询设备失败")
	}

	// 更新设备激活状态
	now := time.Now()
	updates := map[string]interface{}{
		"secret_key":    secretKey,
		"is_activated":  true,
		"activated_at":  &now,
		"status":        1, // 设置为在线状态
	}

	if err := s.db.Model(&device).Updates(updates).Error; err != nil {
		logger.Error("Failed to activate device: %v", err)
		return errors.New("激活设备失败")
	}

	// 记录激活日志
	log := model.DeviceLog{
		DeviceID: device.ID,
		Type:     "activate",
		Content:  "设备激活成功",
	}
	s.db.Create(&log)

	return nil
}

// UpdateDeviceStatus 更新设备状态
func (s *deviceService) UpdateDeviceStatus(sn string, status int, battery, rssi int) error {
	var device model.Device
	if err := s.db.Where("sn = ?", sn).First(&device).Error; err != nil {
		return err
	}

	// 更新设备状态
	now := time.Now()
	updates := map[string]interface{}{
		"status":    status,
		"battery":   battery,
		"rssi":      rssi,
		"last_seen": &now,
	}

	if err := s.db.Model(&device).Updates(updates).Error; err != nil {
		logger.Error("Failed to update device status: %v", err)
		return err
	}

	// 缓存设备状态到Redis
	ctx := context.Background()
	statusKey := fmt.Sprintf("device:status:%s", sn)
	statusData := map[string]interface{}{
		"status":    status,
		"battery":   battery,
		"rssi":      rssi,
		"last_seen": now.Unix(),
	}
	statusJSON, _ := json.Marshal(statusData)
	s.rdb.Set(ctx, statusKey, statusJSON, 24*time.Hour)

	return nil
}

// SendCommand 发送设备命令
func (s *deviceService) SendCommand(deviceID uint, command *DeviceCommand) error {
	// 获取设备信息
	device, err := s.GetDeviceByID(deviceID)
	if err != nil {
		return err
	}

	// 创建命令记录
	cmdRecord := model.DeviceCommand{
		DeviceID: deviceID,
		Type:     command.Type,
		Command:  command.Command,
		Status:   0, // 待执行
	}

	if command.Params != nil {
		paramsJSON, _ := json.Marshal(command.Params)
		cmdRecord.Params = string(paramsJSON)
	}

	if err := s.db.Create(&cmdRecord).Error; err != nil {
		logger.Error("Failed to create device command: %v", err)
		return errors.New("创建设备命令失败")
	}

	// 通过MQTT发送命令
	if err := s.mqttClient.PublishDeviceCommand(device.SN, command); err != nil {
		logger.Error("Failed to send device command via MQTT: %v", err)
		// 更新命令状态为失败
		s.db.Model(&cmdRecord).Updates(map[string]interface{}{
			"status": 3,
			"result": "MQTT发送失败",
		})
		return errors.New("发送设备命令失败")
	}

	// 更新命令状态为执行中
	s.db.Model(&cmdRecord).Update("status", 1)

	return nil
}

// GetDeviceCommands 获取设备命令列表
func (s *deviceService) GetDeviceCommands(deviceID uint, page, pageSize int) (*DeviceCommandListResponse, error) {
	var commands []model.DeviceCommand
	var total int64

	query := s.db.Model(&model.DeviceCommand{}).Where("device_id = ?", deviceID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count device commands: %v", err)
		return nil, errors.New("获取命令总数失败")
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&commands).Error; err != nil {
		logger.Error("Failed to get device commands: %v", err)
		return nil, errors.New("获取命令列表失败")
	}

	return &DeviceCommandListResponse{
		Total: int(total),
		List:  commands,
	}, nil
}

// GetDeviceLogs 获取设备日志
func (s *deviceService) GetDeviceLogs(deviceID uint, page, pageSize int, logType string) (*DeviceLogListResponse, error) {
	var logs []model.DeviceLog
	var total int64

	query := s.db.Model(&model.DeviceLog{}).Where("device_id = ?", deviceID)

	// 日志类型过滤
	if logType != "" {
		query = query.Where("type = ?", logType)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		logger.Error("Failed to count device logs: %v", err)
		return nil, errors.New("获取日志总数失败")
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&logs).Error; err != nil {
		logger.Error("Failed to get device logs: %v", err)
		return nil, errors.New("获取日志列表失败")
	}

	return &DeviceLogListResponse{
		Total: int(total),
		List:  logs,
	}, nil
}

// BatchUpdateDisplay 批量更新显示
func (s *deviceService) BatchUpdateDisplay(deviceIDs []uint, templateID uint) error {
	// 获取设备列表
	var devices []model.Device
	if err := s.db.Where("id IN ?", deviceIDs).Find(&devices).Error; err != nil {
		return errors.New("查询设备失败")
	}

	// 批量发送更新命令
	for _, device := range devices {
		command := &DeviceCommand{
			Type:    "update_display",
			Command: "refresh",
			Params: map[string]interface{}{
				"template_id": templateID,
			},
		}

		if err := s.SendCommand(device.ID, command); err != nil {
			logger.Error("Failed to send update command to device %s: %v", device.SN, err)
		}
	}

	return nil
}
