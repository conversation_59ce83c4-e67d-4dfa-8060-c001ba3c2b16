{"name": "electronic-price-tag-miniprogram", "version": "1.0.0", "description": "电子价签系统小程序端", "main": "main.js", "scripts": {"dev": "uni", "build:mp-weixin": "uni build --platform mp-weixin", "build:mp-alipay": "uni build --platform mp-alipay", "build:mp-baidu": "uni build --platform mp-baidu", "build:mp-toutiao": "uni build --platform mp-toutiao", "build:mp-qq": "uni build --platform mp-qq", "build:h5": "uni build --platform h5", "build:app-plus": "uni build --platform app-plus"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0", "@dcloudio/uni-components": "^3.0.0", "@dcloudio/uni-ui": "^1.4.28", "vue": "^3.3.4", "pinia": "^2.1.6"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "^3.0.0", "@dcloudio/uni-cli-shared": "^3.0.0", "@dcloudio/vite-plugin-uni": "^3.0.0", "vite": "^4.4.5", "typescript": "^5.1.6"}, "uni-app": {"scripts": {}}, "engines": {"node": ">=16.0.0"}}