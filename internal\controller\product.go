package controller

import (
	"electronic-price-tag/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type ProductController struct {
	productService service.ProductService
}

type UpdateProductPriceRequest struct {
	NewPrice int    `json:"new_price" binding:"required"`
	Reason   string `json:"reason" binding:"required"`
}

func NewProductController(productService service.ProductService) *ProductController {
	return &ProductController{
		productService: productService,
	}
}

// GetProducts 获取商品列表
// @Summary 获取商品列表
// @Description 分页获取商品列表
// @Tags 商品管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Param keyword query string false "搜索关键词"
// @Param category_id query int false "分类ID"
// @Success 200 {object} service.ProductListResponse
// @Failure 400 {object} map[string]interface{}
// @Router /api/products [get]
func (c *ProductController) GetProducts(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	keyword := ctx.Query("keyword")
	categoryID, _ := strconv.ParseUint(ctx.Query("category_id"), 10, 32)

	// 获取当前用户的商户ID
	merchantID, _ := ctx.Get("merchant_id")

	response, err := c.productService.GetProducts(merchantID.(string), page, pageSize, keyword, uint(categoryID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": response,
	})
}

// GetProductByID 根据ID获取商品
// @Summary 根据ID获取商品
// @Description 根据商品ID获取商品详细信息
// @Tags 商品管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "商品ID"
// @Success 200 {object} model.Product
// @Failure 400 {object} map[string]interface{}
// @Router /api/products/{id} [get]
func (c *ProductController) GetProductByID(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的商品ID",
		})
		return
	}

	product, err := c.productService.GetProductByID(uint(id))
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code": 404,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": product,
	})
}

// CreateProduct 创建商品
// @Summary 创建商品
// @Description 创建新商品
// @Tags 商品管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body service.CreateProductRequest true "创建商品请求"
// @Success 200 {object} model.Product
// @Failure 400 {object} map[string]interface{}
// @Router /api/products [post]
func (c *ProductController) CreateProduct(ctx *gin.Context) {
	var req service.CreateProductRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	// 设置当前用户的商户ID
	merchantID, _ := ctx.Get("merchant_id")
	req.MerchantID = merchantID.(string)

	product, err := c.productService.CreateProduct(&req)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "创建成功",
		"data": product,
	})
}

// UpdateProduct 更新商品
// @Summary 更新商品
// @Description 更新商品信息
// @Tags 商品管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "商品ID"
// @Param request body service.UpdateProductRequest true "更新商品请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/products/{id} [put]
func (c *ProductController) UpdateProduct(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的商品ID",
		})
		return
	}

	var req service.UpdateProductRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	if err := c.productService.UpdateProduct(uint(id), &req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "更新成功",
	})
}

// DeleteProduct 删除商品
// @Summary 删除商品
// @Description 删除商品
// @Tags 商品管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "商品ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/products/{id} [delete]
func (c *ProductController) DeleteProduct(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的商品ID",
		})
		return
	}

	if err := c.productService.DeleteProduct(uint(id)); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "删除成功",
	})
}

// UpdateProductPrice 更新商品价格
// @Summary 更新商品价格
// @Description 更新商品价格并记录变更日志
// @Tags 商品管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "商品ID"
// @Param request body UpdateProductPriceRequest true "更新价格请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/products/{id}/price [post]
func (c *ProductController) UpdateProductPrice(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的商品ID",
		})
		return
	}

	var req UpdateProductPriceRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, _ := ctx.Get("user_id")

	if err := c.productService.UpdateProductPrice(uint(id), req.NewPrice, req.Reason, userID.(uint)); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "价格更新成功",
	})
}

// GetProductPriceLogs 获取商品价格变更日志
// @Summary 获取商品价格变更日志
// @Description 获取商品的价格变更历史记录
// @Tags 商品管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "商品ID"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} service.ProductPriceLogListResponse
// @Failure 400 {object} map[string]interface{}
// @Router /api/products/{id}/price-logs [get]
func (c *ProductController) GetProductPriceLogs(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的商品ID",
		})
		return
	}

	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))

	response, err := c.productService.GetProductPriceLogs(uint(id), page, pageSize)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": response,
	})
}

// GetCategories 获取商品分类列表
// @Summary 获取商品分类列表
// @Description 获取商品分类树形结构
// @Tags 商品管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} []model.ProductCategory
// @Failure 400 {object} map[string]interface{}
// @Router /api/categories [get]
func (c *ProductController) GetCategories(ctx *gin.Context) {
	// 获取当前用户的商户ID
	merchantID, _ := ctx.Get("merchant_id")

	categories, err := c.productService.GetCategories(merchantID.(string))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code": 500,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "获取成功",
		"data": categories,
	})
}

// CreateCategory 创建商品分类
// @Summary 创建商品分类
// @Description 创建新的商品分类
// @Tags 商品管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param request body service.CreateCategoryRequest true "创建分类请求"
// @Success 200 {object} model.ProductCategory
// @Failure 400 {object} map[string]interface{}
// @Router /api/categories [post]
func (c *ProductController) CreateCategory(ctx *gin.Context) {
	var req service.CreateCategoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	// 设置当前用户的商户ID
	merchantID, _ := ctx.Get("merchant_id")
	req.MerchantID = merchantID.(string)

	category, err := c.productService.CreateCategory(&req)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "创建成功",
		"data": category,
	})
}

// UpdateCategory 更新商品分类
// @Summary 更新商品分类
// @Description 更新商品分类信息
// @Tags 商品管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "分类ID"
// @Param request body service.UpdateCategoryRequest true "更新分类请求"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/categories/{id} [put]
func (c *ProductController) UpdateCategory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的分类ID",
		})
		return
	}

	var req service.UpdateCategoryRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "请求参数错误",
			"data": err.Error(),
		})
		return
	}

	if err := c.productService.UpdateCategory(uint(id), &req); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "更新成功",
	})
}

// DeleteCategory 删除商品分类
// @Summary 删除商品分类
// @Description 删除商品分类
// @Tags 商品管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "分类ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Router /api/categories/{id} [delete]
func (c *ProductController) DeleteCategory(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  "无效的分类ID",
		})
		return
	}

	if err := c.productService.DeleteCategory(uint(id)); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code": 400,
			"msg":  err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 200,
		"msg":  "删除成功",
	})
}
