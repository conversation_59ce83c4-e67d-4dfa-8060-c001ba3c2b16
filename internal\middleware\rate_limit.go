package middleware

import (
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// RateLimiter 限流器接口
type RateLimiter interface {
	Allow(key string) bool
}

// TokenBucketLimiter 令牌桶限流器
type TokenBucketLimiter struct {
	buckets map[string]*TokenBucket
	mutex   sync.RWMutex
	rate    int           // 每秒生成令牌数
	burst   int           // 桶容量
	cleanup time.Duration // 清理间隔
}

// TokenBucket 令牌桶
type TokenBucket struct {
	tokens    int
	lastRefill time.Time
	mutex     sync.Mutex
}

// NewTokenBucketLimiter 创建令牌桶限流器
func NewTokenBucketLimiter(rate, burst int) *TokenBucketLimiter {
	limiter := &TokenBucketLimiter{
		buckets: make(map[string]*TokenBucket),
		rate:    rate,
		burst:   burst,
		cleanup: time.Minute * 5, // 5分钟清理一次
	}
	
	// 启动清理协程
	go limiter.cleanupRoutine()
	
	return limiter
}

// Allow 检查是否允许请求
func (l *TokenBucketLimiter) Allow(key string) bool {
	l.mutex.RLock()
	bucket, exists := l.buckets[key]
	l.mutex.RUnlock()
	
	if !exists {
		l.mutex.Lock()
		// 双重检查
		if bucket, exists = l.buckets[key]; !exists {
			bucket = &TokenBucket{
				tokens:    l.burst,
				lastRefill: time.Now(),
			}
			l.buckets[key] = bucket
		}
		l.mutex.Unlock()
	}
	
	return bucket.takeToken(l.rate, l.burst)
}

// takeToken 获取令牌
func (b *TokenBucket) takeToken(rate, burst int) bool {
	b.mutex.Lock()
	defer b.mutex.Unlock()
	
	now := time.Now()
	elapsed := now.Sub(b.lastRefill)
	
	// 计算应该添加的令牌数
	tokensToAdd := int(elapsed.Seconds()) * rate
	if tokensToAdd > 0 {
		b.tokens += tokensToAdd
		if b.tokens > burst {
			b.tokens = burst
		}
		b.lastRefill = now
	}
	
	// 检查是否有可用令牌
	if b.tokens > 0 {
		b.tokens--
		return true
	}
	
	return false
}

// cleanupRoutine 清理过期的桶
func (l *TokenBucketLimiter) cleanupRoutine() {
	ticker := time.NewTicker(l.cleanup)
	defer ticker.Stop()
	
	for range ticker.C {
		l.mutex.Lock()
		now := time.Now()
		for key, bucket := range l.buckets {
			bucket.mutex.Lock()
			if now.Sub(bucket.lastRefill) > l.cleanup {
				delete(l.buckets, key)
			}
			bucket.mutex.Unlock()
		}
		l.mutex.Unlock()
	}
}

// RateLimit 限流中间件
func RateLimit(limiter RateLimiter, keyFunc func(*gin.Context) string) gin.HandlerFunc {
	return func(c *gin.Context) {
		key := keyFunc(c)
		if !limiter.Allow(key) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"code": 429,
				"msg":  "请求过于频繁，请稍后再试",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// IPRateLimit IP限流中间件
func IPRateLimit(rate, burst int) gin.HandlerFunc {
	limiter := NewTokenBucketLimiter(rate, burst)
	return RateLimit(limiter, func(c *gin.Context) string {
		return c.ClientIP()
	})
}

// UserRateLimit 用户限流中间件
func UserRateLimit(rate, burst int) gin.HandlerFunc {
	limiter := NewTokenBucketLimiter(rate, burst)
	return RateLimit(limiter, func(c *gin.Context) string {
		userID, exists := c.Get("user_id")
		if !exists {
			return c.ClientIP() // 未登录用户使用IP限流
		}
		return "user:" + string(rune(userID.(uint)))
	})
}

// APIRateLimit API限流中间件
func APIRateLimit(rate, burst int) gin.HandlerFunc {
	limiter := NewTokenBucketLimiter(rate, burst)
	return RateLimit(limiter, func(c *gin.Context) string {
		return c.Request.Method + ":" + c.Request.URL.Path
	})
}

// SlidingWindowLimiter 滑动窗口限流器
type SlidingWindowLimiter struct {
	windows map[string]*SlidingWindow
	mutex   sync.RWMutex
	limit   int           // 窗口内最大请求数
	window  time.Duration // 窗口大小
}

// SlidingWindow 滑动窗口
type SlidingWindow struct {
	requests []time.Time
	mutex    sync.Mutex
}

// NewSlidingWindowLimiter 创建滑动窗口限流器
func NewSlidingWindowLimiter(limit int, window time.Duration) *SlidingWindowLimiter {
	return &SlidingWindowLimiter{
		windows: make(map[string]*SlidingWindow),
		limit:   limit,
		window:  window,
	}
}

// Allow 检查是否允许请求
func (l *SlidingWindowLimiter) Allow(key string) bool {
	l.mutex.RLock()
	window, exists := l.windows[key]
	l.mutex.RUnlock()
	
	if !exists {
		l.mutex.Lock()
		if window, exists = l.windows[key]; !exists {
			window = &SlidingWindow{
				requests: make([]time.Time, 0),
			}
			l.windows[key] = window
		}
		l.mutex.Unlock()
	}
	
	return window.addRequest(l.limit, l.window)
}

// addRequest 添加请求
func (w *SlidingWindow) addRequest(limit int, window time.Duration) bool {
	w.mutex.Lock()
	defer w.mutex.Unlock()
	
	now := time.Now()
	cutoff := now.Add(-window)
	
	// 移除过期的请求
	validRequests := make([]time.Time, 0)
	for _, req := range w.requests {
		if req.After(cutoff) {
			validRequests = append(validRequests, req)
		}
	}
	w.requests = validRequests
	
	// 检查是否超过限制
	if len(w.requests) >= limit {
		return false
	}
	
	// 添加当前请求
	w.requests = append(w.requests, now)
	return true
}
